"use client";

import React, { useEffect, useState } from "react";
import ListingCard from "@/components/Cards/ListingCard/ListingCard";
import AdvancedSearchBar from "@/components/AdvancedSearchBar/AdvancedSearchBar";
import { PropertyData } from "@/typescript/interfaces";
import PropertyLocationMap from "@/components/PropertyLocationMap/PropertyLocationMap";
import { MAP_CONSTANTS } from "@/constants/map.constants";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import { fetchListings } from "@/actions/spaces.action";
import useUserLocation from "@/hooks/useUserLocation";
import LoadMoreSkeleton from "./LoadMoreSkeleton";
import { Pagination } from "@/typescript/types";
import GoogleMapsProvider from "@/components/GoogleMapsProvider";

interface ExploreSpacesSectionProps {
  listings: PropertyData[];
  initialQuery?: { [key: string]: any };
  pagination?: Pagination;
}

const ExploreSpacesSection = ({
  listings: initialListings,
  initialQuery = {},
  pagination = { page: 1, limit: 10, total: 0 },
}: ExploreSpacesSectionProps) => {
  const [listings, setListings] = useState<PropertyData[]>(initialListings);
  const [hasMore, setHasMore] = useState(true);
  const { location, error } = useUserLocation();
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [isFiltering, setIsFiltering] = useState(false);
  const [paginationData, setPaginationData] = useState<Pagination>(pagination);

  // 🧠 Centralized fetch function
  const loadListings = async (pageNumber: number, append = false) => {
    const queryObj: { [key: string]: any } = { ...initialQuery };

    if (location && !initialQuery?.fullAddress) {
      queryObj.location = {
        coordinates: [location.lng, location.lat],
        radius: 500,
      };
    }

    queryObj.page = pageNumber;
    queryObj.limit = paginationData.limit || 10;

    const res = await fetchListings(queryObj);

    const newListings = res?.result?.records || [];
    const newPage = res?.result?.page || 1;
    const newLimit = res?.result?.limit || 10;
    const newTotal = res?.result?.total || 0;

    const totalFetched = append ? listings.length + newListings.length : newListings.length;

    setHasMore(totalFetched < newTotal);

    setPaginationData({
      page: newPage,
      limit: newLimit,
      total: newTotal,
    });

    if (append) {
      setListings((prev) => [...prev, ...newListings]);
    } else {
      setListings(newListings);
    }
  };

  // 🔁 First load or filters change
  useEffect(() => {
    if (!initialQuery?.fullAddress) {
      setHasMore(true);
      setIsFiltering(false);
      loadListings(1).finally(() => setIsFiltering(false));
    }
  }, [location]);

  // 🤖 Handle Load More
  const handleSeeMoreSpaces = async () => {
    const nextPage = paginationData.page + 1;
    setIsLoadingMore(true);
    await loadListings(nextPage, true);
    setIsLoadingMore(false);
  };

  const handleMapBoundsChange = async (center: { lat: number; lng: number }, radius: number) => {
    const queryObj = {
      ...initialQuery,
      location: {
        coordinates: [center.lng, center.lat],
        radius,
      },
      page: 1,
      limit: paginationData.limit,
    };

    setIsLoadingMore(true);
    const res = await fetchListings(queryObj);
    const newListings = res?.result?.records || [];
    const total = res?.result?.total || 0;

    setListings(newListings);
    setPaginationData({
      page: 1,
      limit: paginationData.limit,
      total,
    });
    setHasMore(newListings.length < total);
    setIsLoadingMore(false);
  };

  useEffect(() => {
    if (error) {
      console.log("Location Error:", error);
    }
  }, [error]);

  return (
    <section>
      <div className="container space-y-4">
        <AdvancedSearchBar
          setListings={setListings}
          setIsLoadingMore={setIsLoadingMore}
          setIsFiltering={setIsFiltering}
          isLoadingMore={isLoadingMore || isFiltering}
        />

        <div className="grid grid-cols-12 gap-5">
          {/* Listings */}
          <div className="col-span-12 lg:col-span-8">
            {isFiltering && (
              <div className="mb-6">
                <div className="text-center py-8">
                  <div className="inline-flex items-center gap-2 px-4 py-2 bg-blue-50 text-blue-600 rounded-full">
                    <div className="w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full animate-spin"></div>
                    Filtering listings...
                  </div>
                </div>
                <LoadMoreSkeleton />
              </div>
            )}

            {isLoadingMore && !isFiltering && (
              <div className="mt-6">
                <LoadMoreSkeleton />
              </div>
            )}

            {!isFiltering && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-[30px]">
                {listings.length > 0 ? (
                  listings.map((listing: PropertyData, idx: number) => (
                    <ListingCard key={`${listing._id || idx}`} listing={listing} />
                  ))
                ) : (
                  <div className="col-span-full text-center py-12">
                    <div className="max-w-sm mx-auto">
                      <div className="text-6xl mb-4">🏠</div>
                      <h3 className="text-xl font-semibold text-gray-900 mb-2">
                        No Listings Found
                      </h3>
                      <p className="text-gray-600">
                        Try adjusting your search filters or check back later for new listings.
                      </p>
                    </div>
                  </div>
                )}
              </div>
            )}

            {hasMore && !isFiltering && listings.length > 0 && (
              <div className="text-center mt-12">
                <Button
                  className="px-8! py-6!"
                  onClick={handleSeeMoreSpaces}
                  disabled={isLoadingMore}
                >
                  {isLoadingMore ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                      Loading More...
                    </>
                  ) : (
                    <>
                      See More Spaces
                      <DynamicIcon name="between-horizontal-start" />
                    </>
                  )}
                </Button>
              </div>
            )}

            {!hasMore && listings.length > 0 && !isFiltering && (
              <p className="text-center mt-8 text-muted-foreground">No more spaces to load</p>
            )}
          </div>

          {/* Map */}
          <div className="col-span-12 lg:col-span-4">
            <div className="sticky top-0 rounded-lg overflow-hidden">
              <GoogleMapsProvider>
                <PropertyLocationMap
                  listings={listings}
                  width="100%"
                  height="100vh"
                  center={location || MAP_CONSTANTS.DEFAULT_CENTER}
                  onRadiusChange={handleMapBoundsChange}
                  setListings={setListings}
                  setIsLoadingMore={setIsLoadingMore}
                />
              </GoogleMapsProvider>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExploreSpacesSection;
