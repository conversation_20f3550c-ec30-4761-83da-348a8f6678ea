"use client";

import React, { useState } from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm, UseFormReturn } from "react-hook-form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  allowPets,
  amenities,
  bathRooms,
  bedRooms,
  neighborhood,
  numberOfOccupants,
  parkings,
  residenceTypes,
  spaceTypes,
} from "@/constants/constants";
import { Button } from "@/components/ui/button";
import { Search, SlidersVertical, Loader2 } from "lucide-react";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { MultiSelect } from "@/components/ui/MultiSelect";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { AdvancedSearchFormValues, advancedSearchFormSchema } from "@/lib/validations";
import { zodResolver } from "@hookform/resolvers/zod";
import { ScrollArea } from "@/components/ui/scroll-area";
import { PropertyData } from "@/typescript/interfaces";
import { fetchListings } from "@/actions/spaces.action";
import GoogleMapsProvider from "../GoogleMapsProvider";
import LocationAutocomplete from "../LocationAutocomplete/LocationAutocomplete";
import { DynamicIcon } from "lucide-react/dynamic";

interface Args {
  setListings: (listings: PropertyData[]) => void;
  setIsLoadingMore: (value: boolean) => void;
  isLoadingMore?: boolean;
  setIsFiltering: (value: boolean) => void;
}

const AdvancedSearchBar = ({
  setListings,
  setIsLoadingMore,
  isLoadingMore = false,
  setIsFiltering,
}: Args) => {
  const [isSearching, setIsSearching] = useState(false);

  const form = useForm<AdvancedSearchFormValues>({
    resolver: zodResolver(advancedSearchFormSchema),
    defaultValues: {
      fullAddress: "",
      spaceType: "",
      residenceType: "",
      monthlyBudget: 0,
      bedrooms: [],
      bathrooms: [],
      numPeopleInHome: [],
      allowedPets: [],
      parking: [],
      neighborhood: [],
      amenities: [],
    },
  });

  const onSubmit = async (data: AdvancedSearchFormValues) => {
    // console.log("Advanced Search Data: ", data);
    // Checking all values are null or empty and only send query truthly values
    const filteredData = Object.fromEntries(
      Object.entries(data).filter(([, value]) => {
        if (Array.isArray(value)) return value.length > 0;
        if (typeof value === "string") return value.trim() !== "";
        if (typeof value === "number") return value > 0;
        return value !== null && value !== undefined;
      }),
    );

    try {
      setIsSearching(true);
      setIsLoadingMore(true);
      setIsFiltering(true);
      const res = await fetchListings(filteredData);
      setListings(res?.result?.records || []);
    } catch (error: unknown) {
      console.log("Error", error);
    } finally {
      setIsSearching(false);
      setIsLoadingMore(false);
      setIsFiltering(false);
    }
  };

  // const handleReset = () => {
  //   const resetValues = {
  //     fullAddress: "",
  //     spaceType: "",
  //     residenceType: "",
  //     monthlyBudget: 0,
  //     bedrooms: [],
  //     bathrooms: [],
  //     numPeopleInHome: [],
  //     allowedPets: [],
  //     parkingRequired: [],
  //     neighborhood: [],
  //     amenities: [],
  //   };
  //   form.reset(resetValues);
  // };

  const isLoading = isSearching || isLoadingMore;

  return (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(onSubmit)}
        id="advanced-search-form"
        className="bg-white p-[30px] border shadow-md rounded-md lg:rounded-full grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 items-center gap-4"
      >
        <FormField
          name="fullAddress"
          control={form.control}
          render={({}) => (
            <FormItem className="w-full md:border-e-2 hidden md:block">
              <FormLabel className="mb-2 text-[#575757] text-[15px] font-medium">
                Location
              </FormLabel>
              <GoogleMapsProvider>
                <div className="relative">
                  <LocationAutocomplete
                    className="border-none placeholder:text-[14px]"
                    placeholder="Search Location"
                    onSelect={(data) => {
                      form.setValue("fullAddress", data.address);
                    }}
                  />
                  <div className="absolute top-1/2 -translate-y-1/2 right-[10px]">
                    <DynamicIcon name="locate-fixed" size={18} color={"#555555"} />
                  </div>
                </div>
              </GoogleMapsProvider>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="spaceType"
          control={form.control}
          render={({ field }) => (
            <FormItem className="w-full md:border-e-2 hidden md:block">
              <FormLabel className="mb-2 text-[#575757] text-[15px] font-medium">
                Space Type
              </FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={isLoading}
              >
                <FormControl>
                  <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {spaceTypes.map((spaceType, idx) => (
                    <SelectItem key={idx} value={spaceType.value}>
                      {spaceType.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="residenceType"
          control={form.control}
          render={({ field }) => (
            <FormItem className="w-full md:border-e-2 hidden md:block">
              <FormLabel className="mb-2 text-[#575757] text-[15px] font-medium">
                House Type
              </FormLabel>
              <Select
                onValueChange={field.onChange}
                defaultValue={field.value}
                disabled={isLoading}
              >
                <FormControl>
                  <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                    <SelectValue placeholder="All" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {residenceTypes.map((residence, idx) => (
                    <SelectItem key={idx} value={residence.value}>
                      {residence.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="col-span-1 md:col-span-full lg:col-span-1 space-y-4">
          <MobileFilters form={form} isLoading={isLoading} />
          <AdvancedSearch form={form} disabled={isLoading} />
        </div>

        <div className="col-span-1 md:col-span-full lg:col-span-1">
          <Button
            type="submit"
            className="flex-1 w-full h-auto px-4 py-3 rounded-full text-[15px]! font-normal shadow-none"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Searching...
              </>
            ) : (
              <>
                Search <Search />
              </>
            )}
          </Button>

          {/* <Button
            type="button"
            variant="outline"
            className="h-auto px-4 py-3 rounded-full"
            onClick={handleReset}
            disabled={isLoading}
          >
            Reset
          </Button> */}
        </div>
      </form>
    </Form>
  );
};

const AdvancedSearch = ({
  form,
  disabled = false,
}: {
  form: UseFormReturn<AdvancedSearchFormValues>;
  disabled?: boolean;
}) => {
  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button
          variant="outline"
          className="w-full h-auto px-4 py-3 rounded-full border-secondary text-[15px]! text-[#555555] font-normal"
          disabled={disabled}
        >
          Advanced Search <SlidersVertical />
        </Button>
      </DialogTrigger>
      <DialogContent className="lg:max-w-[1200px] 2xl:min-w-[1500px] rounded-[20px]! p-[35px]! text-[#555555]">
        <ScrollArea className="max-h-[680px]">
          <DialogHeader className="mb-8">
            <DialogTitle className="text-[20px] font-semibold">Advanced Search</DialogTitle>
          </DialogHeader>
          <div className="space-y-[45px]">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField
                name="monthlyBudget"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="text-[16px] font-medium">
                      Monthly Budget: ${field.value || 0}
                    </FormLabel>
                    <FormControl className="mt-[10px]">
                      <Slider
                        min={0}
                        max={10000}
                        value={[field.value ?? 0]}
                        onValueChange={(value) => field.onChange(value[0])}
                        disabled={disabled}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-[24px]">
              <FormField
                name="bedrooms"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full gap-[10px]">
                    <FormLabel className="text-[16px] font-medium">Bedrooms</FormLabel>
                    <FormControl>
                      <MultiSelect
                        className="input-field"
                        options={bedRooms}
                        onValueChange={field.onChange}
                        disabled={disabled}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                name="bathrooms"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full gap-[10px]">
                    <FormLabel className="text-[16px] font-medium">Bathrooms</FormLabel>
                    <FormControl>
                      <MultiSelect
                        className="input-field"
                        options={bathRooms}
                        onValueChange={field.onChange}
                        disabled={disabled}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                name="numPeopleInHome"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full gap-[10px]">
                    <FormLabel className="text-[16px] font-medium">
                      Total Occupants in Home
                    </FormLabel>
                    <MultiSelect
                      className="input-field"
                      options={numberOfOccupants}
                      onValueChange={field.onChange}
                      disabled={disabled}
                    />
                  </FormItem>
                )}
              />

              <FormField
                name="allowedPets"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full gap-[10px]">
                    <FormLabel className="text-[16px] font-medium">Allow Pets</FormLabel>
                    <FormControl>
                      <MultiSelect
                        className="input-field"
                        options={allowPets}
                        onValueChange={field.onChange}
                        disabled={disabled}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                name="parking"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full gap-[10px]">
                    <FormLabel className="text-[16px] font-medium">Parking</FormLabel>
                    <FormControl>
                      <MultiSelect
                        className="input-field"
                        options={parkings}
                        onValueChange={field.onChange}
                        disabled={disabled}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />

              <FormField
                name="neighborhood"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full gap-[10px]">
                    <FormLabel className="text-[16px] font-medium">Neighborhood</FormLabel>
                    <FormControl>
                      <MultiSelect
                        className="input-field"
                        options={neighborhood}
                        onValueChange={field.onChange}
                        disabled={disabled}
                      />
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <div>
              <FormField
                name="amenities"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full gap-[25px]">
                    <FormLabel className="text-[18px] font-semibold">Amenities</FormLabel>
                    <FormControl>
                      <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-4 gap-4">
                        {amenities.map((amenity) => (
                          <div key={amenity.value} className="flex items-center gap-2">
                            <Checkbox
                              id={amenity.value}
                              checked={field.value?.includes(amenity.value)}
                              onCheckedChange={(checked) => {
                                return checked
                                  ? field.onChange([...(field.value ?? []), amenity.value])
                                  : field.onChange(
                                      field.value?.filter(
                                        (value: string) => value !== amenity.value,
                                      ),
                                    );
                              }}
                              disabled={disabled}
                            />
                            <FormLabel htmlFor={amenity.value} className="text-base font-normal">
                              {amenity.label}
                            </FormLabel>
                          </div>
                        ))}
                      </div>
                    </FormControl>
                  </FormItem>
                )}
              />
            </div>

            <DialogClose asChild>
              <Button
                type="submit"
                form="advanced-search-form"
                disabled={disabled}
                className="text-[16px] px-[22px] py-[15px] font-normal"
              >
                {disabled ? (
                  <>
                    <Loader2 className="w-4 h-4 animate-spin" />
                    Searching...
                  </>
                ) : (
                  "Update Search"
                )}
              </Button>
            </DialogClose>
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
};

const MobileFilters = ({ form, isLoading }: { form: any; isLoading?: boolean }) => {
  const [isFilterOpen, setIsFilterOpen] = React.useState(false);

  return (
    <div className="block md:hidden w-full">
      <Drawer open={isFilterOpen} onOpenChange={setIsFilterOpen}>
        <DrawerTrigger asChild>
          <Button
            variant={"outline"}
            className="w-full h-auto px-4 py-3 rounded-full shadow-none text-gray-600 border-secondary"
            onClick={() => setIsFilterOpen((prev) => !prev)}
          >
            Filters <SlidersVertical size={20} />
          </Button>
        </DrawerTrigger>

        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Filters</DrawerTitle>
            <DrawerDescription>Customize your search to get better results.</DrawerDescription>
          </DrawerHeader>

          <Form {...form}>
            <form className="p-4 space-y-4" id="mobile-advanced-filter-form">
              {/* Location */}
              <FormField
                name="fullAddress"
                control={form.control}
                render={({}) => (
                  <FormItem className="w-full">
                    <FormLabel className="input-label">Location</FormLabel>
                    <GoogleMapsProvider>
                      <LocationAutocomplete
                        className="input-field"
                        placeholder="Search Location"
                        onSelect={(data) => {
                          form.setValue("fullAddress", data.address);
                        }}
                      />
                    </GoogleMapsProvider>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* Space Type */}
              <FormField
                name="spaceType"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="input-label">Space Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="rounded-full border w-full h-auto p-5">
                          <SelectValue placeholder="Select Space Type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {spaceTypes.map((spaceType, idx) => (
                          <SelectItem key={idx} value={spaceType.value}>
                            {spaceType.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              {/* House Type */}
              <FormField
                name="residenceType"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="input-label">House Type</FormLabel>
                    <Select
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      disabled={isLoading}
                    >
                      <FormControl>
                        <SelectTrigger className="rounded-full border w-full h-auto p-5">
                          <SelectValue placeholder="Select House Type" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {residenceTypes.map((residence, idx) => (
                          <SelectItem key={idx} value={residence.value}>
                            {residence.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <div className="flex flex-col gap-3">
                <DrawerClose asChild>
                  <Button
                    type="submit"
                    form="advanced-search-form"
                    className="w-full rounded-full"
                    disabled={isLoading}
                  >
                    {isLoading ? (
                      <>
                        <Loader2 className="w-4 h-4 animate-spin mr-2" />
                        Searching...
                      </>
                    ) : (
                      "Apply Filters"
                    )}
                  </Button>
                </DrawerClose>

                <DrawerClose asChild>
                  <Button
                    type="button"
                    variant="outline"
                    className="w-full rounded-full"
                    onClick={() => form.reset()}
                  >
                    Reset Filters
                  </Button>
                </DrawerClose>
              </div>
            </form>
          </Form>
        </DrawerContent>
      </Drawer>
    </div>
  );
};

export default AdvancedSearchBar;
