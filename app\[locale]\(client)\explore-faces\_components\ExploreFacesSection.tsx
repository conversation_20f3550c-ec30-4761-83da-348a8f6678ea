"use client";

import React, { useState } from "react";
import FacesFilter from "./FacesFilter";
import UserCard from "@/components/Cards/UserCard/UserCard";
import { UserProfile } from "@/typescript/interfaces";
import { getAllFaces } from "@/actions/faces.action";

interface ExploreFacesSectionProps {
  initialUsers: UserProfile[];
}

const ExploreFacesSection = ({ initialUsers }: ExploreFacesSectionProps) => {
  const [users, setUsers] = useState(initialUsers);
  const [loading, setLoading] = useState(false);

  const onFilterChange = async (filters: any) => {
    // Filter out empty values (empty strings, null, undefined)
    const filteredQuery = Object.fromEntries(
      Object.entries(filters).filter(
        ([, value]) => value !== "" && value !== null && value !== undefined,
      ),
    );

    // If no filters are applied, show all initial users
    if (Object.keys(filteredQuery).length === 0) {
      setUsers(initialUsers);
      return;
    }

    // Apply filters
    setLoading(true);
    try {
      // const queryString = new URLSearchParams(
      //   Object.entries(filteredQuery).map(([key, value]) => [key, String(value)]),
      // ).toString();

      const res = await getAllFaces(filteredQuery);
      setUsers(res?.result?.records || []);
    } catch (error) {
      console.error("Error fetching filtered users:", error);
      setUsers([]);
    } finally {
      setLoading(false);
    }
  };

  return (
    <section className="pb-[40px]">
      <div className="container space-y-8">
        <FacesFilter onFilterChange={onFilterChange} />

        {loading ? (
          <div className="text-center py-8">
            <p className="text-gray-600">Loading filtered results...</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
            {users?.length > 0 ? (
              users.map((user) => <UserCard key={user._id} user={user} />)
            ) : (
              <div className="col-span-full text-center py-8">
                <p className="text-gray-500">No users found matching your criteria</p>
              </div>
            )}
          </div>
        )}
      </div>
    </section>
  );
};

export default ExploreFacesSection;
