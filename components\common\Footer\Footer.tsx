import React from "react";
import Link from "next/link";
import Logo from "../Logo";
import SubscribeForm from "@/components/Forms/SubscribeForm/SubscribeForm";
import { footerNavigations, socialLinks } from "@/data";
import { IFooterLinks, ISocialLink, Navigation } from "@/typescript/interfaces";
import { FaRegEnvelope } from "react-icons/fa";
import { GrLocation } from "react-icons/gr";
import Image from "next/image";
import { DynamicIcon } from "lucide-react/dynamic";

const Footer = (): React.JSX.Element => {
  const currentYear = new Date().getFullYear();
  const playStore = "/images/stores/play-store.webp";
  const appStore = "/images/stores/apple-store.webp";
  return (
    <footer className="bg-secondary text-white">
      <div className="px-4">
        <div className="flex flex-row items-center md:items-center justify-between gap-4 py-5">
          <Logo variant="light" className="w-40!" />
          <div className="flex items-center gap-10">
            <p className="hidden md:block text-lg lg:text-xl text-white">Follow us on</p>
            <ul className="flex items-center gap-10">
              {socialLinks.map((link: ISocialLink, idx: number) => (
                <li key={idx}>
                  <Link href={link.href} target="_blank">
                    <link.icon size={24} />
                  </Link>
                </li>
              ))}
            </ul>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:flex lg:flex-auto lg:flex-row flex-wrap gap-7 border-y-[0.5px] pt-[48px] pb-[30px]">
          <div className="flex-2 ">
            <h3 className="text-[19px] font-medium mb-4 md:mb-6 lg:mb-[30px]">Subscribe</h3>
            <SubscribeForm />
          </div>
          {footerNavigations.map((footerLink: IFooterLinks) => (
            <FooterItems footerLink={footerLink} key={footerLink.title} />
          ))}
          <div className="flex-1">
            <h3 className="text-[19px] font-medium mb-4 md:mb-6 lg:mb-[30px]">Contact Us</h3>
            <ul className="space-y-[10px]">
              <li>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-sm md:text-base hover:underline flex gap-2"
                >
                  <FaRegEnvelope size={20} /> <EMAIL>
                </Link>
              </li>
              <li>
                <Link
                  href="mailto:<EMAIL>"
                  className="text-sm md:text-base hover:underline flex gap-2"
                >
                  <DynamicIcon name="phone" size={20} /> (*************
                </Link>
              </li>
              <li className="flex gap-2">
                <GrLocation size={20} />
                <address className="text-sm md:text-base hover:underline">
                  99 Fifth avense, 3rd Floor,
                  <br /> New York, NY 10001
                </address>
              </li>
            </ul>
          </div>
          <div className="flex-1">
            <h3 className="text-[19px] font-medium mb-4 md:mb-6 lg:mb-[30px]">Get The App</h3>
            <div className="flex flex-col gap-[10px]">
              <Link href="#" className="w-36 block">
                <Image
                  src={playStore}
                  alt="Play Store"
                  width={350}
                  height={40}
                  className="w-full h-auto"
                />
              </Link>
              <Link href="#" className="w-36 block">
                <Image
                  src={appStore}
                  alt="App Store"
                  width={350}
                  height={40}
                  className="w-full h-auto"
                />
              </Link>
            </div>
          </div>
        </div>
        <div className="text-center">
          <p className="text-white pt-[10px] pb-[22px] text-[17px] font-normal">
            &copy; Copyright {currentYear} gayroom8.com. All Rights Reserved.
          </p>
        </div>
      </div>
    </footer>
  );
};

const FooterItems = ({ footerLink }: { footerLink: IFooterLinks }) => {
  return (
    <div className="flex-1">
      <h3 className="text-[19px] font-medium mb-4 md:mb-6 lg:mb-[30px]">{footerLink.title}</h3>
      <ul className="space-y-[15px]">
        {footerLink.links.map((link: Navigation, idx: number) => (
          <li key={idx}>
            <Link href={link.href} className="text-sm md:text-base hover:underline">
              {link.label}
            </Link>
          </li>
        ))}
      </ul>
    </div>
  );
};

export default Footer;
