import React from "react";
import AccountDetailsForm from "./AccountDetailsForm";
import PasswordChangeForm from "./PasswordChangeForm";
import NotificationPreferencesForm from "./NotificationPreferencesForm";
import AccountStatusForm from "./AccountStatusForm";
import MembershipStatus from "./MembershipStatus";
import AccountProfileCard from "./AccountProfileCard";
import AccountVerification from "./AccountVerification";
import { UserProfile } from "@/typescript/interfaces";
import EmailChangeForm from "./EmailChangeForm";

const SettingsPageSection = async ({ user }: { user: UserProfile }) => {
  // console.log(user);
  return (
    <section>
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 lg:col-span-4 xl:col-span-3">
            <AccountProfileCard user={user} />
          </div>
          <div className="col-span-12 lg:col-span-8 xl:col-span-9 space-y-6">
            <AccountDetailsForm user={user} />
            <EmailChangeForm user={user} />
            <PasswordChangeForm />
            <AccountVerification />
            <NotificationPreferencesForm user={user} />
            <AccountStatusForm user={user} />
            <MembershipStatus user={user} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default SettingsPageSection;
