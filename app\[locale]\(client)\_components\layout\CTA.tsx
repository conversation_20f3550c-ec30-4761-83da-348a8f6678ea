"use client";

import React from "react";
import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { AuthStore, authStore } from "@/store/authStore";
import { useTranslations } from "next-intl";

const CTA = () => {
  const { isAuthenticated } = authStore((state: AuthStore) => state);
  const t = useTranslations("HomePage.CTA");
  const title = t("title");
  const description = t("description");
  const buttonText = t("buttonText");

  return (
    <section className="py-[60px] xl:py-16 bg-[url('/images/home/<USER>')]">
      <div className="container">
        <div className="space-y-5 w-full md:w-3/4 lg:w-2/5">
          <h2 className="text-3xl xl:text-[40px] font-semibold text-gray-50">{title}</h2>
          <p className="text-gray-50 text-base font-medium leading-relaxed">{description}</p>
          <Button
            asChild
            className="px-15! py-3 w-auto h-auto rounded-full bg-white hover:bg-gray-200 text-gray-800"
          >
            <Link href={isAuthenticated ? "/dashboard/my-spaces" : "/auth/register"}>
              {buttonText}
            </Link>
          </Button>
        </div>
      </div>
    </section>
  );
};

export default CTA;
