"use client";

import React from "react";
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  ageRanges,
  geneders,
  sexualOrientations,
  spaceTypes,
  userType,
} from "@/constants/constants";
import { Button } from "@/components/ui/button";
import { useDebounce } from "@/hooks/useDebounce";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DynamicIcon } from "lucide-react/dynamic";
import {
  Drawer,
  DrawerClose,
  DrawerContent,
  DrawerDescription,
  DrawerHeader,
  DrawerTitle,
  DrawerTrigger,
} from "@/components/ui/drawer";
import { RiLoopLeftFill } from "react-icons/ri";
import GoogleMapsProvider from "@/components/GoogleMapsProvider";
import LocationAutocomplete from "@/components/LocationAutocomplete/LocationAutocomplete";

interface FacesFilterProps {
  onFilterChange?: (filters: any) => void;
}

const FacesFilter = ({ onFilterChange }: FacesFilterProps) => {
  const form = useForm({
    defaultValues: {
      intent: "",
      spaceType: "",
      genderIdentity: "",
      sexualOrientation: "",
      age: "",
      preferredLocation: {
        coordinates: [0, 0],
        radius: 1000,
      },
    },
  });

  const handleSubmit = React.useCallback(
    (values: any) => {
      // console.log("handle submit value: ", values);
      onFilterChange?.(values);
    },
    [onFilterChange],
  );

  const debouncedSubmit = useDebounce(handleSubmit, 300);

  const handleSelectChange = (
    fieldName:
      | "sexualOrientation"
      | "intent"
      | "spaceType"
      | "genderIdentity"
      | "age"
      | "preferredLocation",
    value: string,
  ) => {
    form.setValue(fieldName, value);
    const values = form.getValues();
    debouncedSubmit(values);
  };

  const handleLocationChange = (latLng: { lat: number; lng: number }) => {
    form.setValue("preferredLocation", {
      coordinates: [latLng.lng, latLng.lat],
      radius: 1000,
    });
    const values = form.getValues();
    debouncedSubmit(values);
  };

  const handleReset = () => {
    const resetValues = {
      intent: "",
      spaceType: "",
      genderIdentity: "",
      sexualOrientation: "",
      age: "",
      // preferredLocation: {
      //   coordinates: [0, 0],
      //   radius: 1000,
      // },
    };

    form.reset(resetValues);
    // Immediately trigger filter change to show all users
    onFilterChange?.(resetValues);
  };

  return (
    <Form {...form}>
      <form className="space-y-4">
        <div className="bg-white py-5 pr-5 pl-8 border shadow-md rounded-full grid grid-cols-1 lg:grid-cols-6 items-center gap-4 divide-x-0 lg:divide-x-2 border-[#E8E8E8]">
          <FormField
            name="intent"
            control={form.control}
            render={({ field }) => (
              <FormItem className="hidden lg:block">
                <FormLabel className="mb-2 text-[#575757] text-[15px] font-medium">
                  User Type
                </FormLabel>
                <Select
                  onValueChange={(val) => handleSelectChange("intent", val)}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {userType.map((user, idx) => (
                      <SelectItem key={idx} value={user.value}>
                        {user.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            name="spaceType"
            control={form.control}
            render={({ field }) => (
              <FormItem className="hidden lg:block">
                <FormLabel className="mb-2 text-[#575757] text-[15px] font-medium">
                  Space Type
                </FormLabel>
                <Select
                  onValueChange={(val) => handleSelectChange("spaceType", val)}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {spaceTypes.map((space, idx) => (
                      <SelectItem key={idx} value={space.value}>
                        {space.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            name="genderIdentity"
            control={form.control}
            render={({ field }) => (
              <FormItem className="hidden lg:block">
                <FormLabel className="mb-2 text-[#575757] text-[15px] font-medium">
                  Gender
                </FormLabel>
                <Select
                  onValueChange={(val) => handleSelectChange("genderIdentity", val)}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {geneders.map((gender, idx) => (
                      <SelectItem key={idx} value={gender.value}>
                        {gender.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            name="sexualOrientation"
            control={form.control}
            render={({ field }) => (
              <FormItem className="hidden lg:block">
                <FormLabel className="mb-2 text-[#575757] text-[15px] font-medium">
                  Orientation
                </FormLabel>
                <Select
                  onValueChange={(val) => handleSelectChange("sexualOrientation", val)}
                  value={field.value}
                >
                  <FormControl>
                    <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {sexualOrientations.map((orientation, idx) => (
                      <SelectItem key={idx} value={orientation.value}>
                        {orientation.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <FormField
            name="age"
            control={form.control}
            render={({ field }) => (
              <FormItem className="hidden lg:block">
                <FormLabel className="mb-2 text-[#575757] text-[15px] font-medium">Age</FormLabel>
                <Select onValueChange={(val) => handleSelectChange("age", val)} value={field.value}>
                  <FormControl>
                    <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                      <SelectValue placeholder="All" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {ageRanges.map((age, idx) => (
                      <SelectItem key={idx} value={age.value}>
                        {age.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />

          <div className="flex justify-center flex-row items-center gap-4 lg:hidden">
            <Dialog>
              <DialogTrigger asChild>
                <Button
                  variant={"outline"}
                  className="w-full h-auto px-4 py-3 rounded-full shadow-none text-[#555555] flex-1 mr-4"
                >
                  Location <DynamicIcon name="search" size={20} />
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Search By Location</DialogTitle>
                </DialogHeader>
                <GoogleMapsProvider>
                  <LocationAutocomplete
                    className="input-field"
                    placeholder="Enter your location"
                    onSelect={(data) => {
                      handleLocationChange(data.latLng);
                    }}
                  />
                </GoogleMapsProvider>
              </DialogContent>
            </Dialog>
            <MobileFilter
              form={form}
              handleSelectChange={handleSelectChange}
              handleReset={handleReset}
            />
          </div>

          <Button
            type="button"
            className="w-full h-auto px-4 py-3 rounded-full shadow-none hidden lg:flex gap-2"
            onClick={handleReset}
          >
            Reset Filters <RiLoopLeftFill size={24} />
          </Button>
        </div>
      </form>
    </Form>
  );
};

const MobileFilter = ({
  form,
  handleSelectChange,
  handleReset,
}: {
  form: any;
  handleSelectChange: (
    fieldName: "sexualOrientation" | "intent" | "spaceType" | "genderIdentity" | "age",
    value: string,
  ) => void;
  handleReset: () => void;
}) => {
  const [isFilterOpen, setIsFilterOpen] = React.useState(false);

  return (
    <div className="block lg:hidden w-full flex-1">
      <Drawer open={isFilterOpen} onOpenChange={setIsFilterOpen}>
        <DrawerTrigger asChild>
          <Button
            variant={"outline"}
            className="w-full h-auto px-4 py-3 rounded-full shadow-none text-gray-600"
            onClick={() => setIsFilterOpen((prev) => !prev)}
          >
            Filters <DynamicIcon name="sliders-horizontal" size={20} />
          </Button>
        </DrawerTrigger>
        <DrawerContent>
          <DrawerHeader>
            <DrawerTitle>Filters</DrawerTitle>
            <DrawerDescription>Select your filters to find the perfect match.</DrawerDescription>
          </DrawerHeader>
          <Form {...form}>
            <form className="space-y-4">
              <div className="bg-white p-8 border shadow-md rounded-md lg:rounded-full grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 xl:grid-cols-7 items-center gap-4">
                <FormField
                  name="intent"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="block lg:hidden">
                      <FormLabel className="input-label">User Type</FormLabel>
                      <Select
                        onValueChange={(val) => handleSelectChange("intent", val)}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                            <SelectValue placeholder="Select User Type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {userType.map((user, idx) => (
                            <SelectItem key={idx} value={user.value}>
                              {user.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                <FormField
                  name="spaceType"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="block lg:hidden">
                      <FormLabel className="input-label">Space Type</FormLabel>
                      <Select
                        onValueChange={(val) => handleSelectChange("spaceType", val)}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                            <SelectValue placeholder="Select Space Type" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {spaceTypes.map((space, idx) => (
                            <SelectItem key={idx} value={space.value}>
                              {space.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                <FormField
                  name="genderIdentity"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="block lg:hidden">
                      <FormLabel className="input-label">Gender</FormLabel>
                      <Select
                        onValueChange={(val) => handleSelectChange("genderIdentity", val)}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                            <SelectValue placeholder="Select Gender" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {geneders.map((gender, idx) => (
                            <SelectItem key={idx} value={gender.value}>
                              {gender.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                <FormField
                  name="sexualOrientation"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="block lg:hidden">
                      <FormLabel className="input-label">Orientation</FormLabel>
                      <Select
                        onValueChange={(val) => handleSelectChange("sexualOrientation", val)}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                            <SelectValue placeholder="Select Orientation" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {sexualOrientations.map((orientation, idx) => (
                            <SelectItem key={idx} value={orientation.value}>
                              {orientation.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                <FormField
                  name="age"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="block lg:hidden">
                      <FormLabel className="input-label">Age</FormLabel>
                      <Select
                        onValueChange={(val) => handleSelectChange("age", val)}
                        value={field.value}
                      >
                        <FormControl>
                          <SelectTrigger className="rounded-full border-0 w-full !h-auto shadow-none">
                            <SelectValue placeholder="Select Age" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          {ageRanges.map((age, idx) => (
                            <SelectItem key={idx} value={age.value}>
                              {age.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </FormItem>
                  )}
                />

                <Button
                  type="button"
                  variant={"outline"}
                  className="w-full h-auto px-4 py-3 rounded-full shadow-none block lg:hidden"
                  onClick={handleReset}
                >
                  Reset Filters
                </Button>
                <DrawerClose asChild>
                  <Button
                    type="button"
                    variant={"destructive"}
                    className="w-full h-auto px-4 py-3 rounded-full shadow-none"
                  >
                    Close
                  </Button>
                </DrawerClose>
              </div>
            </form>
          </Form>
        </DrawerContent>
      </Drawer>
    </div>
  );
};

export default FacesFilter;
