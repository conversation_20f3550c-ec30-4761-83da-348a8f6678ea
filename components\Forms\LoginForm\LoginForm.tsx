"use client";

import React from "react";
import Link from "next/link";
import Logo from "@/components/common/Logo";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Button } from "@/components/ui/button";
import { FaInstagram } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { zodResolver } from "@hookform/resolvers/zod";
import { loginFormSchema, LoginFormValues } from "@/lib/validations";
import { jwtDecode } from "jwt-decode";
import { handleLogin } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { authStore } from "@/store/authStore";
import { useRouter } from "next/navigation";
import { UserJwtPayload } from "@/typescript/interfaces";
import GoogleIcon from "@/icons/GoogleIcon";
import FacebookIcon from "@/icons/FacebookIcon";
import { useCookies } from "react-cookie";
import { handleSocialRedirect } from "@/utils/socialRedirect";
import { Card, CardContent } from "@/components/ui/card";

const LoginForm = () => {
  const router = useRouter();
  const { setIsAuthenticated, setUser } = authStore((state) => state);
  const [, setCookie] = useCookies(["token"]);

  // Create a form with the provided schema and default values
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginFormSchema),
    defaultValues: {
      email: "",
      password: "",
      rememberMe: false,
    },
  });

  // Check if form is valid
  const isValidForm = form.formState.isValid;

  const onSubmit = async (data: LoginFormValues) => {
    const response = await handleLogin(data);

    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message);
      setCookie("token", response.token, {
        path: "/",
        secure: false,
        maxAge: 30 * 24 * 60 * 60,
      });
      const decodedToken: UserJwtPayload = jwtDecode(response.token);
      setUser({
        email: decodedToken.email as string,
        firstName: decodedToken.firstName as string,
        lastName: decodedToken.lastName as string,
        _id: decodedToken.userId as string,
        avatar: {
          originalName: decodedToken.avatar.originalName as string,
          fileName: decodedToken.avatar.fileName as string,
          fileSize: decodedToken.avatar.fileSize as number,
          mimeType: decodedToken.avatar.mimeType as string,
          filePath: decodedToken.avatar.filePath as string,
        },
      });
      setIsAuthenticated(true);
      router.push("/dashboard/settings");
    } else {
      toast.error(response.message);
      setIsAuthenticated(false);
    }
  };

  // useEffect(() => {
  //   if (searchParams.get("login") === "true") {
  //     setOpen(true);
  //   }
  // }, [searchParams]);

  return (
    <Card className="py-[48px]">
      <CardContent className="space-y-4">
        <div className="w-full max-w-lg space-y-5 mx-auto">
          <div className="text-center mb-[30px]">
            <Logo className="w-[155px]! mx-auto mb-4 block" />
            <h2 className="text-[43px] font-bold text-gray-600 mt-[31px] mb-[4px]">Log In</h2>
            <p className="text-gray-600">
              Don’t have an account?{" "}
              <Link href={"/auth/register"} className="text-secondary underline">
                Sign Up
              </Link>
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-[24px]">
              <FormField
                name="email"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      E-Mail <span className="field-required" />
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="email"
                        className="input-field"
                        placeholder="Enter Your E-Mail"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="password"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Password <span className="field-required" />
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        type="password"
                        className="input-field"
                        placeholder="Enter Your Password"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex items-center justify-between">
                <FormField
                  name="rememberMe"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center">
                      <FormControl>
                        <Checkbox checked={field.value} onCheckedChange={field.onChange} />
                      </FormControl>
                      <FormLabel className="font-normal! text-base text-[#555555]">
                        Remember me
                      </FormLabel>
                    </FormItem>
                  )}
                />
                <Link href={"#"} className="text-gray-600 underline">
                  Forgot your password
                </Link>
                <FormMessage />
              </div>

              {/* <DialogClose asChild> */}
              <Button
                type="submit"
                className="w-full h-auto py-3 rounded-full text-lg cursor-pointer"
                disabled={!isValidForm}
              >
                Login
              </Button>
              {/* </DialogClose> */}

              <div className="relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t after:border-border">
                <span className="relative z-10 bg-background px-4 text-muted-foreground text-[20px]">
                  OR
                </span>
              </div>
            </form>
          </Form>
          <div className="space-y-4 mt-4">
            <Button
              className="w-full py-6 text-[#555] text-[16px]"
              variant={"outline"}
              onClick={() => handleSocialRedirect(router, "google", "auth")}
            >
              <GoogleIcon /> Log In with Google
            </Button>
            <Button
              className="w-full py-6 text-[#555] text-[16px]"
              variant={"outline"}
              onClick={() => {
                console.log("You clicked social button");
              }}
            >
              <FaInstagram />
              Log In with Instagram
            </Button>
            <Button
              className="w-full py-6 text-[#555] text-[16px]"
              variant={"outline"}
              onClick={() => {
                console.log("You clicked social button");
              }}
            >
              <FacebookIcon /> Log In with Facebook
            </Button>
            <Button
              className="w-full py-6 text-[#555] text-[16px]"
              variant={"outline"}
              onClick={() => {
                console.log("You clicked social button");
              }}
            >
              <FaXTwitter />
              Log In with X
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default LoginForm;
