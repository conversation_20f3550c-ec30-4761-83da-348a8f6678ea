"use client";

import React, { useState } from "react";
import { GoogleMap, OverlayView } from "@react-google-maps/api";
import { MAP_CONSTANTS } from "@/constants/map.constants";
import PropertyListingPopup from "./PropertyListingPopup";
import { PropertyData } from "@/typescript/interfaces";

interface PropertyLocationMapProps {
  center: {
    lat: number;
    lng: number;
  };
  listing?: PropertyData;
  listings?: PropertyData[];
  width?: string;
  height?: string;
  onCenterChange?: (lat: number, lng: number) => void;
  setListings?: (listings: PropertyData[]) => void;
  setIsLoadingMore?: (isLoading: boolean) => void;
  onRadiusChange?: (center: { lat: number; lng: number }, radius: number) => void;
}

const PropertyLocationMap = ({
  center,
  listing,
  listings,
  width = "100%",
  height = "500px",
  onCenterChange,
  onRadiusChange,
}: PropertyLocationMapProps) => {
  const [map, setMap] = useState<google.maps.Map | null>(null);

  const centerPoint = React.useMemo(() => {
    return {
      lat: center.lat || MAP_CONSTANTS.DEFAULT_CENTER.lat,
      lng: center.lng || MAP_CONSTANTS.DEFAULT_CENTER.lng,
    };
  }, [center.lat, center.lng]);

  // Boundaries to restrict user from moving map out of view
  const bounds = {
    north: 85,
    south: -85,
    east: 180,
    west: -180,
  };

  const onLoad = React.useCallback(function callback(map: google.maps.Map) {
    // const bounds = new window.google.maps.LatLngBounds(center);
    map.setCenter(centerPoint);
    setMap(map);
  }, []);

  const onUnmount = React.useCallback(function callback() {
    setMap(null);
  }, []);

  const handleIdle = React.useCallback(() => {
    if (map) {
      const center = map.getCenter();
      const bounds = map.getBounds();

      if (center && bounds) {
        const radiusInMeters = window.google.maps.geometry.spherical.computeDistanceBetween(
          center,
          bounds.getNorthEast(),
        );

        onCenterChange?.(center.lat(), center.lng());
        onRadiusChange?.({ lat: center.lat(), lng: center.lng() }, Math.round(radiusInMeters));
      }
    }
  }, [map]);

  return (
    <GoogleMap
      mapContainerStyle={{ width, height }}
      center={centerPoint}
      zoom={MAP_CONSTANTS.DEFAULT_ZOOM}
      onLoad={onLoad}
      onUnmount={onUnmount}
      onIdle={handleIdle}
      options={{
        mapTypeControl: true,
        streetViewControl: false,
        fullscreenControl: false,
        zoomControl: true,
        minZoom: 1,
        restriction: {
          latLngBounds: bounds,
          strictBounds: true,
        },
      }}
    >
      {/* Single Listing */}
      {listing && (
        <OverlayView position={centerPoint} mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}>
          {listing && <PropertyListingPopup listing={listing} />}
        </OverlayView>
      )}
      {/* Multiple Listings */}
      {listings &&
        listings.map((listing) => {
          const coordinates = (listing?.location as { coordinates: [number, number] })?.coordinates;
          return (
            <OverlayView
              key={listing._id}
              position={{
                lat: coordinates?.[1] as number,
                lng: coordinates?.[0] as number,
              }}
              mapPaneName={OverlayView.OVERLAY_MOUSE_TARGET}
            >
              <PropertyListingPopup listing={listing} />
            </OverlayView>
          );
        })}
    </GoogleMap>
  );
};

export default PropertyLocationMap;
