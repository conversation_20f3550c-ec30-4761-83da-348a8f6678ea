import { Blog } from "@/typescript/types";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const handleGetBlogs = async () => {
  try {
    const response = await fetch(`${baseURL}/web/blog`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.json();
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const handleGetBlogBySlug = async (
  slug: string,
): Promise<{
  message: string;
  blog: Blog | null;
}> => {
  try {
    const response = await fetch(`${baseURL}/web/blog/${slug}`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.json();
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        blog: null,
      };
    } else {
      return {
        message: "An error occurred",
        blog: null,
      };
    }
  }
};
