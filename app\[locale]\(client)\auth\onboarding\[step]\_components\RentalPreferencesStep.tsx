"use client";

import { handleSignup } from "@/actions/auth.action";
import GoogleMapsProvider from "@/components/GoogleMapsProvider";
// import { GeoLocation } from "@/components/GeoLocation/GeoLocation";
// import GoogleMapsProvider from "@/components/GoogleMapsProvider";
import LocationAutocomplete from "@/components/LocationAutocomplete/LocationAutocomplete";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import {
  bathRooms,
  bedRooms,
  findPets,
  wantFurnished,
  willingToSignRentalAgreement,
} from "@/constants/constants";
import { RentalPrefsData, rentalPrefsSchema } from "@/lib/validations/sign-up-schema";
import { useSignupStore } from "@/store/userSignUpStore";
import { formatNumberWithCommas, isValidNumberInput, removeCommas } from "@/utils/numberFormatter";
import { zodResolver } from "@hookform/resolvers/zod";
import { DynamicIcon } from "lucide-react/dynamic";
import { useRouter } from "next/navigation";
import React from "react";
import { useForm } from "react-hook-form";
import toast from "react-hot-toast";

const RentalPreferencesStep = () => {
  const router = useRouter();
  const {
    currentStep,
    steps,
    data,
    setCurrentStep,
    clear,
    setUserData,
    markStepCompleted,
    resetCompletedSteps,
  } = useSignupStore();
  const form = useForm<RentalPrefsData>({
    resolver: zodResolver(rentalPrefsSchema),
    defaultValues: {
      preferredLocation: data.user.preferredLocation || [0, 0],
      preferredLocationLabel: data.user.preferredLocationLabel || "",
      rentalStartDate: data.user.rentalStartDate || "",
      rentalDuration: data.user.rentalDuration || 1,
      maxMonthlyBudget: data.user?.maxMonthlyBudget ?? 0,
      willingToSignRentalAgreement: data.user.willingToSignRentalAgreement || "",
      wantFurnished: data.user.wantFurnished || "",
      bedrooms: data.user.bedrooms || "",
      bathrooms: data.user.bathrooms || "",
      pets: data.user.pets || "",
      parkingRequired: data.user.parkingRequired || "",
    },
  });

  async function onSubmit(values: RentalPrefsData) {
    const preferredLocation: [number, number] = [
      values.preferredLocation[0],
      values.preferredLocation[1],
    ];
    // setPropertyData(values);
    setUserData({ ...values, preferredLocation });
    const updatedData = useSignupStore.getState().data;

    const fullDataToSend = {
      ...updatedData,
      property: {
        ...updatedData.property,
        ...values,
      },
    };

    // console.log("Rental Preferences form data:", fullDataToSend);

    // const isLastStep = currentStep === steps.length - 1;
    // if (isLastStep) {
    //   router.push("/");
    //   setCurrentStep(0);
    //   clear();
    // } else {
    //   router.push(`/auth/onboarding/${currentStep + 1}`);
    // }
    const res = await handleSignup(fullDataToSend);
    if (res.status >= 200 && res.status < 300) {
      toast.success(res.message);
      markStepCompleted(currentStep);
      resetCompletedSteps();
      setCurrentStep(0);
      clear();
      router.replace("/auth/thank-you");
    } else {
      toast.error(res.message);
      router.replace("/");
      setCurrentStep(0);
      resetCompletedSteps();
      clear();
    }
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  // const handleNext = () => {
  //   if (!form.formState.isValid) return;
  //   if (currentStep === steps.length - 1) return;
  //   router.push(`/auth/onboarding/${currentStep + 1}`);
  // };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="preferredLocation"
          control={form.control}
          render={({}) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Location<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <GoogleMapsProvider>
                  <LocationAutocomplete
                    placeholder="Enter your location"
                    className="input-field"
                    onSelect={(data) => {
                      form.setValue("preferredLocation", [data.latLng.lng, data.latLng.lat]);
                      form.setValue("preferredLocationLabel", data.address);
                    }}
                  />
                </GoogleMapsProvider>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="rentalStartDate"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Rental Start Date<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Rental Start Date"
                  type="date"
                  {...field}
                  className="input-field"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="rentalDuration"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Rental Duration<span className="text-red-500">*</span>
              </FormLabel>
              {field.value <= 11 ? (
                <>
                  <FormControl className="mt-2">
                    <Slider
                      min={1}
                      step={1}
                      max={12}
                      value={[field.value || 1]}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                  </FormControl>
                  <p className="text-sm text-muted-foreground">{field.value || 1} Months</p>
                </>
              ) : (
                <FormControl>
                  <Select
                    defaultValue={field.value === 12 ? "12" : "11"}
                    onValueChange={(value) => field.onChange(parseInt(value))}
                  >
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="11">Less than 1 Year</SelectItem>
                      <SelectItem value="12">1 Year</SelectItem>
                      <SelectItem value="13">1 Year+</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              )}
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="maxMonthlyBudget"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Max Monthly Budget<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <DynamicIcon
                    name="dollar-sign"
                    size={16}
                    className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-600"
                  />
                  <Input
                    placeholder="Max Monthly Budget"
                    {...field}
                    value={formatNumberWithCommas(field.value || "")}
                    onChange={(e) => {
                      const raw = removeCommas(e.target.value);
                      if (isValidNumberInput(raw)) {
                        field.onChange(raw);
                      }
                    }}
                    className="input-field pl-8"
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="willingToSignRentalAgreement"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Willing to Sign a Rental Agreement?<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {willingToSignRentalAgreement.map((agreement) => (
                      <SelectItem key={agreement.value} value={agreement.value}>
                        {agreement.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="wantFurnished"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Want it Furnished?<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {wantFurnished.map((item) => (
                      <SelectItem key={item.value} value={item.value}>
                        {item.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bedrooms"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Bedrooms<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {bedRooms.map((bedRoom) => (
                      <SelectItem key={bedRoom.value} value={bedRoom.value}>
                        {bedRoom.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bathrooms"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Bathrooms<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {bathRooms.map((bathroom) => (
                      <SelectItem key={bathroom.value} value={bathroom.value}>
                        {bathroom.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="pets"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Pets?<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {findPets.map((pet) => (
                      <SelectItem key={pet.value} value={pet.value}>
                        {pet.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="parkingRequired"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Parking Required?<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="yes">Yes</SelectItem>
                    <SelectItem value="no">No</SelectItem>
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Complete" : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default RentalPreferencesStep;
