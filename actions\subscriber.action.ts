const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const handleSubscribe = async (membershipId: string) => {
  const token = document.cookie
    .split("; ")
    .find((row) => row.startsWith("token="))
    ?.split("=")[1];
  try {
    const response = await fetch(`${baseURL}/web/memberships/subscribe`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ membershipId }),
    });

    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        message: resData.message,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const handleManageSubscription = async (token: string) => {
  // const token = document.cookie
  //   .split("; ")
  //   .find((row) => row.startsWith("token="))
  //   ?.split("=")[1];
  try {
    const response = await fetch(`${baseURL}/web/user/me/stripe/create-portal-session`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        message: resData.message,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};
