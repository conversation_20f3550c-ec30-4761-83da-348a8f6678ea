"use client";

import React from "react";
import Link from "next/link";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { userNavigations } from "@/data";
import { DynamicIcon } from "lucide-react/dynamic";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { AuthStore, authStore } from "@/store/authStore";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useCookies } from "react-cookie";

const UserNavigation = () => {
  const router = useRouter();
  const { user } = authStore((state: AuthStore) => state);
  const [, , removeCookie] = useCookies(["token"]);

  const profileUrl = user?.avatar?.filePath
    ? process.env.NEXT_PUBLIC_CDN_URL + "/" + user?.avatar?.filePath + "/" + user?.avatar?.fileName
    : "";

  const handleLogout = () => {
    authStore.setState({ user: null, isAuthenticated: false });
    removeCookie("token", { path: "/" });
    router.push("/");
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild className="cursor-pointer shadow-md">
        <Avatar>
          <Avatar>
            <AvatarImage src={profileUrl} />
            <AvatarFallback>{user?.firstName || "N/A"}</AvatarFallback>
          </Avatar>
        </Avatar>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="space-y-3 p-5" sideOffset={20}>
        <DropdownMenuLabel>
          <h3 className="text-lg font-semibold text-secondary">{user?.firstName || "N/A"}</h3>
          <p>{user?.email || "N/A"}</p>
        </DropdownMenuLabel>
        <DropdownMenuSeparator className="bg-primary" />
        {userNavigations.map((navigation) => (
          <DropdownMenuItem key={navigation.label} asChild className="cursor-pointer">
            <Link href={navigation.href}>
              <DynamicIcon name={navigation.icon} size={16} />
              {navigation.label}
            </Link>
          </DropdownMenuItem>
        ))}
        <DropdownMenuItem asChild className="w-full cursor-pointer">
          <Button
            onClick={handleLogout}
            variant={"ghost"}
            className="justify-start font-normal px-2!"
          >
            <DynamicIcon name="log-out" size={16} /> Logout
          </Button>
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
};

export default UserNavigation;
