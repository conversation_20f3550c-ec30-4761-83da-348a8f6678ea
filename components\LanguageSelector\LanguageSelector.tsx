"use client";

import { usePathname, useRouter } from "@/i18n/navigation";
import { useLocale } from "next-intl";
import React, { useState } from "react";
import ReactFlagsSelect from "react-flags-select";

const flagToLocaleMap: Record<string, string> = {
  US: "en-US",
  GB: "en-GB",
  DE: "de",
};

const localeToFlagMap: Record<string, string> = {
  "en-US": "US",
  "en-GB": "GB",
  de: "DE",
};

const LanguageSelector = () => {
  const router = useRouter();
  const pathname = usePathname();
  const locale = useLocale();

  const currentLocale = locale;
  const defaultFlag = localeToFlagMap[currentLocale] || "US";

  const [selected, setSelected] = useState<string>(defaultFlag);

  const handleChangeLanguage = (countryCode: string) => {
    const newLocale = flagToLocaleMap[countryCode];
    setSelected(countryCode);

    // Switch language without changing route
    router.replace(pathname, { locale: newLocale });
  };

  return (
    <div>
      <ReactFlagsSelect
        countries={["US", "GB"]}
        customLabels={{ US: "English US", GB: "English UK" }}
        selected={selected}
        onSelect={handleChangeLanguage}
        className="pb-0!"
        selectButtonClassName="rounded-full! py-[6px]! px-[10px]! shadow-md!"
        showSelectedLabel={false}
        showOptionLabel={false}
        fullWidth={false}
      />
    </div>
  );
};

export default LanguageSelector;
