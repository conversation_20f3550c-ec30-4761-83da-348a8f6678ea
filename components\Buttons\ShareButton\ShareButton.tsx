"use client";

import React from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, PopoverTrigger } from "@/components/ui/popover";
import { But<PERSON> } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import {
  FacebookShareButton,
  FacebookIcon,
  TwitterShareButton,
  WhatsappShareButton,
  WhatsappIcon,
  XIcon,
  ThreadsShareButton,
  ThreadsIcon,
} from "react-share";
import { useCopyToClipboard } from "@/hooks/useCopyToClipboard";

interface Args {
  url: string;
  title?: string;
}

const ShareButton = ({ url = "", title = "gayroom8" }: Args) => {
  const [, copy] = useCopyToClipboard();
  const hashtag = "#gayroom8";

  const handleCopy = async () => {
    await copy(url);
  };

  return (
    <Popover>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          className="rounded-[10px] px-[34px]! py-[20px]! text-base font-normal text-[#555555]"
        >
          {" "}
          <DynamicIcon name="share-2" size={16} /> Share
        </Button>
      </PopoverTrigger>
      <PopoverContent>
        <div className="flex items-center gap-4">
          <FacebookShareButton url={url} hashtag={hashtag} title={title}>
            <FacebookIcon size={32} round />
          </FacebookShareButton>
          <ThreadsShareButton url={url} title={title}>
            <ThreadsIcon size={32} round />
          </ThreadsShareButton>
          <TwitterShareButton url={url} title={title}>
            <XIcon size={32} round />
          </TwitterShareButton>
          <WhatsappShareButton url={url} title={title}>
            <WhatsappIcon size={32} round />
          </WhatsappShareButton>
          <Button onClick={handleCopy} variant={"outline"} className="rounded-full" size={"icon"}>
            <DynamicIcon name="copy" size={32} />
          </Button>
        </div>
      </PopoverContent>
    </Popover>
  );
};

export default ShareButton;
