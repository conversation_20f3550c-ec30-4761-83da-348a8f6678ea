"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  bathRoom,
  bedroomSize,
  currentPets,
  furnished,
  parkingRequires,
  willingToSignRentalAgreement,
} from "@/constants/constants";
import { RentalPrefsData, rentalPrefsSchema } from "@/lib/validations/profileForm.schema";
// import { GeoLocation } from "@/components/GeoLocation/GeoLocation";
import { Input } from "@/components/ui/input";
import { UserProfile } from "@/typescript/interfaces";
import { updateUserProfile } from "@/actions/user.action";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { useCookies } from "react-cookie";
import LocationAutocomplete from "@/components/LocationAutocomplete/LocationAutocomplete";
import GoogleMapsProvider from "@/components/GoogleMapsProvider";

interface Args {
  isEdit: boolean;
  setIsEdit: (value: boolean) => void;
  user: UserProfile;
}

const RentalPreferencesForm = ({ isEdit, setIsEdit, user }: Args) => {
  const [cookies] = useCookies(["token"]);
  const token = cookies.token;
  const router = useRouter();
  const form = useForm<RentalPrefsData>({
    resolver: zodResolver(rentalPrefsSchema),
    defaultValues: {
      preferredLocation: user?.preferredLocation || [0, 0],
      preferredLocationLabel: user?.preferredLocationLabel || "",
      rentalStartDate: user?.rentalStartDate || "",
      // rentalDuration: user?.rentalDuration || 0,
      maxMonthlyBudget: user?.maxMonthlyBudget || 0,
      willingToSignRentalAgreement: user?.willingToSignRentalAgreement || "",
      wantFurnished: user?.wantFurnished || "",
      bedroomSize: user?.bedroomSize || "",
      bathRoom: user?.bathroom || "",
      pets: user?.pets || "",
      parkingRequired: user?.parkingRequired || "",
    },
  });
  const onSubmit = async (data: RentalPrefsData) => {
    const response = await updateUserProfile(data, token);
    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message);
      setIsEdit(!isEdit);
      router.refresh();
    } else {
      toast.error(response.message);
      setIsEdit(!isEdit);
      router.refresh();
    }
    // form.reset();
  };

  return (
    <div className="space-y-7 bg-white shadow p-4 rounded-md border">
      <div className="space-y-3 relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="sliders-horizontal" size={25} /> Rental Preferences
        </h2>
      </div>
      <Form {...form}>
        <form className="space-y-5" onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            name="preferredLocation"
            control={form.control}
            render={({}) => (
              <FormItem>
                <FormLabel className="input-label">Preferred Location</FormLabel>
                <FormControl>
                  <GoogleMapsProvider>
                    <LocationAutocomplete
                      className="input-field"
                      placeholder="Enter your location"
                      onSelect={(data) => {
                        form.setValue("preferredLocation", [data.latLng.lng, data.latLng.lat]);
                        form.setValue("preferredLocationLabel", data.address);
                      }}
                    />
                  </GoogleMapsProvider>
                  {/* <GeoLocation
                    onSelect={(data) => {
                      form.setValue("preferredLocation", [data.latLng.lng, data.latLng.lat]);
                      form.setValue("preferredLocationLabel", data.address);
                    }}
                  /> */}
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="rentalStartDate"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Rental Start Date</FormLabel>
                <FormControl>
                  <Input type="date" className="input-field" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          {/* <FormField
            name="rentalDuration"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Rental Duration</FormLabel>
                <FormControl>
                  <Input type="number" className="input-field" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          /> */}
          <FormField
            name="maxMonthlyBudget"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Max Monthly Budget</FormLabel>
                <FormControl>
                  <Input type="number" className="input-field" {...field} min={0} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="willingToSignRentalAgreement"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Willing to Sign Rental Agreement</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="input-field px-3! py-6!">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {willingToSignRentalAgreement.map((agreement) => (
                      <SelectItem key={agreement.value} value={agreement.value}>
                        {agreement.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="wantFurnished"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Want Furnished</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="input-field px-3! py-6!">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {furnished.map((furnish) => (
                      <SelectItem key={furnish.value} value={furnish.value}>
                        {furnish.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="bedroomSize"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Bedroom Size</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="input-field px-3! py-6!">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {bedroomSize.map((size) => (
                      <SelectItem key={size.value} value={size.value}>
                        {size.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="bathRoom"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Bathroom</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="input-field px-3! py-6!">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {bathRoom.map((bathroom) => (
                      <SelectItem key={bathroom.value} value={bathroom.value}>
                        {bathroom.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="pets"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Pets</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="input-field px-3! py-6!">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {currentPets.map((pet) => (
                      <SelectItem key={pet.value} value={pet.value}>
                        {pet.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="parkingRequired"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Parking Required</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="input-field px-3! py-6!">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {parkingRequires.map((parking) => (
                      <SelectItem key={parking.value} value={parking.value}>
                        {parking.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="w-full max-w-sm mx-auto ">
            <Button type="submit" className="w-full h-auto py-3 rounded-full cursor-pointer">
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default RentalPreferencesForm;
