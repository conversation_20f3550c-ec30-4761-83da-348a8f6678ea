import React from "react";
import PlanCard from "@/components/Cards/PlanCard/PlanCard";
import { Plan } from "@/typescript/types";
import { getAllMemberships } from "@/actions/membership.action";

const MembershipPage = async () => {
  const plans = (await getAllMemberships())?.memberships;
  return (
    <section className="py-[40px]">
      <div className="container">
        <div className="space-y-4 text-center mb-[58px]">
          <h2 className="text-3xl lg:text-4xl font-semibold text-secondary">Subscribe</h2>
          <p>Messaging in the gayroom8 Community Requires a Subscription</p>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5 mt-8">
          {plans.length > 0 ? (
            plans.map(
              (plan: Plan, idx: number) =>
                plan.isActive && <PlanCard key={idx} plan={plan} index={idx} />,
            )
          ) : (
            <p>No Plans Found</p>
          )}
        </div>
      </div>
    </section>
  );
};

export default MembershipPage;
