import { ChatContent } from "@/typescript/interfaces";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const createConversation = async (userId: string, token: string) => {
  // const token = document.cookie
  //   .split("; ")
  //   .find((row) => row.startsWith("token="))
  //   ?.split("=")[1];
  try {
    const response = await fetch(`${baseURL}/web/conversation`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ userId }),
    });
    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const sendMessage = async (conversationId: string, content: ChatContent) => {
  const token = document.cookie
    .split("; ")
    .find((row) => row.startsWith("token="))
    ?.split("=")[1];
  try {
    const response = await fetch(`${baseURL}/web/conversation/${conversationId}/chat`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(content),
    });
    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const getMessages = async (conversationId: string) => {
  const token = document.cookie
    .split("; ")
    .find((row) => row.startsWith("token="))
    ?.split("=")[1];
  try {
    const response = await fetch(`${baseURL}/web/conversation/${conversationId}/chat`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    const resData = await response.json();
    console.log(resData);
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};
