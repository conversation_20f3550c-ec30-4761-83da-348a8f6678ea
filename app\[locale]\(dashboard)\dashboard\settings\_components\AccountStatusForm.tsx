"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Switch } from "@/components/ui/switch";
import { AccountStatusData, accountStatusSchema } from "@/lib/validations/settings";
import { handleAccountStatus } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { UserProfile } from "@/typescript/interfaces";
import { useCookies } from "react-cookie";

const AccountStatusForm = ({ user }: { user: UserProfile }) => {
  const [cookies] = useCookies(["token"]);
  const form = useForm<AccountStatusData>({
    resolver: zod<PERSON><PERSON><PERSON>ver(accountStatusSchema),
    defaultValues: {
      profile: user?.profileStatus || false,
      listing: user?.listingStatus || false,
    },
  });

  const handleSwitchChange =
    (name: string, onChange: (value: boolean) => void) => async (value: boolean) => {
      onChange(value);
      const token = cookies.token;
      const backendPayload = {
        type: name.replace(/([A-Z])/g, "_$1").toLowerCase(),
        status: value,
      };

      const response = await handleAccountStatus(backendPayload, token);
      if (response.status >= 200 && response.status < 300) {
        toast.success(response.message);
      } else {
        toast.error(response.message);
      }
    };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl text-secondary font-medium flex items-center gap-2.5">
          <DynamicIcon name="user-round-check" size={24} /> Account Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form className="space-y-5">
            <FormField
              name="profile"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <FormLabel className="input-label">User Profile Status (visible)</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleSwitchChange(field.name, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="listing"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <FormLabel className="input-label">Listing Status (visible)</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleSwitchChange(field.name, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default AccountStatusForm;
