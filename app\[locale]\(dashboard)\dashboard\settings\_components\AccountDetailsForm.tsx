"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { AccountDetailsData, accountDetailsSchema } from "@/lib/validations/settings";
import { UserProfile } from "@/typescript/interfaces";
import { handleUpdateAccountDetails } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { useCookies } from "react-cookie";
import { useRouter } from "next/navigation";

interface AccountDetailsFormProps {
  user: UserProfile;
}

const AccountDetailsForm = ({ user }: AccountDetailsFormProps) => {
  const router = useRouter();
  const [cookies] = useCookies(["token"]);
  const form = useForm<AccountDetailsData>({
    resolver: zodResolver(accountDetailsSchema),
    defaultValues: {
      firstName: user?.firstName || "",
      lastName: user?.lastName || "",
      phone: user?.phone || "",
    },
  });

  const onSubmit = async (data: AccountDetailsData) => {
    const response = await handleUpdateAccountDetails(data, cookies.token);
    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message);
      router.refresh();
    } else {
      toast.error(response.message);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl text-secondary font-medium flex items-center gap-2.5">
          <DynamicIcon name="user" size={24} /> Account Details
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <FormField
                name="firstName"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">First Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Your First Name"
                        {...field}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="lastName"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">Last Name</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Your Last Name"
                        {...field}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="phone"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">Phone</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter Your Phone" {...field} className="input-field" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="mt-8 text-center">
              <Button type="submit" className="h-auto px-20 py-3 rounded-full cursor-pointer">
                Update
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default AccountDetailsForm;
