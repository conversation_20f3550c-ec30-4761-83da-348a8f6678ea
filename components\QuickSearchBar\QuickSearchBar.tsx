"use client";

import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Form, FormControl, FormField, FormItem, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { quickSearchBarSchema, QuickSearchBarValues } from "@/lib/validations";
import LocationAutocomplete from "../LocationAutocomplete/LocationAutocomplete";
import GoogleMapsProvider from "../GoogleMapsProvider";
import { Skeleton } from "../ui/skeleton";

interface QuickSearchBarProps {
  className?: string;
}

const QuickSearchBar = ({ className }: QuickSearchBarProps): React.JSX.Element => {
  const [activeTab, setActiveTab] = useState<string>("explore-spaces");

  return (
    <div className={`${className}`}>
      <Tabs defaultValue="explore-spaces" onValueChange={(value: string) => setActiveTab(value)}>
        <TabsList className="bg-transparent h-auto gap-3">
          <TabsTrigger
            value="explore-spaces"
            className="p-2 md:px-4 md:py-3.5 h-auto bg-white! text-secondary text-[16px] md:text-[20px] font-medium data-[state=active]:bg-secondary! data-[state=active]:text-white cursor-pointer gap-[10px] rounded-[10px]"
          >
            <DynamicIcon name="map-pin-house" size={24} />
            Explore Spaces
          </TabsTrigger>
          <TabsTrigger
            value="explore-faces"
            className="p-2 md:px-4 md:py-3.5 h-auto bg-white! text-secondary text-[16px] md:text-[20px] font-medium data-[state=active]:bg-secondary! data-[state=active]:text-white cursor-pointer gap-[10px] rounded-[10px]"
          >
            <DynamicIcon name="user-round-search" size={24} />
            Explore Faces
          </TabsTrigger>
        </TabsList>
        <TabsContent value="explore-spaces">
          <SearchBar activeTab={activeTab} placeholder={"Enter an address, neighborhood city."} />
        </TabsContent>
        <TabsContent value="explore-faces">
          <SearchBar activeTab={activeTab} placeholder={"Search by user name"} />
        </TabsContent>
      </Tabs>
    </div>
  );
};

const SearchBar = ({ activeTab, placeholder }: { activeTab: string; placeholder: string }) => {
  const router = useRouter();
  const form = useForm({
    resolver: zodResolver(quickSearchBarSchema),
    defaultValues: {
      search: "",
    },
  });

  const onSubmit = (data: QuickSearchBarValues) => {
    // Redirect based on active tab
    if (activeTab === "explore-spaces") {
      router.push(`/explore-spaces?fullAddress=${encodeURIComponent(data.search)}`);
    } else if (activeTab === "explore-faces") {
      router.push(`/explore-faces?searchText=${encodeURIComponent(data.search)}`);
    } else {
      return;
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        {activeTab === "explore-spaces" ? (
          <FormField
            name="search"
            control={form.control}
            render={({}) => (
              <FormItem className="relative">
                <div className="relative">
                  <FormControl>
                    <GoogleMapsProvider
                      fallback={
                        <Skeleton className="pl-[65px]! pr-5 py-[14px] md:py-[20px]! md:pl-6 md:pr-28 flex items-center bg-white">
                          Loading Searchbar...
                        </Skeleton>
                      }
                    >
                      <LocationAutocomplete
                        className="bg-white pl-[65px]! pr-5 py-[14px] md:py-[20px]! md:pl-6 md:pr-28 placeholder:text-sm relative placeholder:text-[14px]! rounded-[10px]"
                        placeholder={placeholder}
                        onSelect={(data) => {
                          form.setValue("search", data.address);
                        }}
                        form={form}
                        formValue="search"
                      />
                      <div className="absolute top-1/2  -translate-y-1/2 left-[24px]">
                        <DynamicIcon name="search" size={24} color="#828282" />
                      </div>
                    </GoogleMapsProvider>
                  </FormControl>
                </div>
                <Button
                  type="submit"
                  className="lg:absolute lg:top-0 lg:right-0 lg:bottom-0 p-[10px] px-[35px]! rounded-bl-none rounded-tl-none cursor-pointer rounded-l-[10px]  lg:rounded-l-none rounded-r-[10px] h-auto text-base md:text-[20px] font-normal"
                >
                  Search
                </Button>
                <FormMessage />
              </FormItem>
            )}
          />
        ) : (
          <FormField
            name="search"
            control={form.control}
            render={({ field }) => (
              <FormItem className="relative">
                <div className="relative">
                  <FormControl>
                    <Input
                      {...field}
                      className="bg-white pl-[65px]! pr-24 py-[14px] md:py-[20px]! md:pl-6 md:pr-28 h-auto relative rounded-[10px]"
                      placeholder={placeholder}
                    />
                  </FormControl>
                  <div className="absolute top-1/2 -translate-y-1/2 left-[24px]">
                    <DynamicIcon name="search" size={24} color="#828282" />
                  </div>
                </div>
                <Button
                  type="submit"
                  className="lg:absolute lg:top-0 lg:right-0 lg:bottom-0 p-[10px] px-[35px]! rounded-bl-none rounded-tl-none cursor-pointer rounded-l-[10px]  lg:rounded-l-none rounded-r-[10px] h-auto text-base md:text-[20px] font-normal"
                >
                  Search
                </Button>
                <FormMessage />
              </FormItem>
            )}
          />
        )}
      </form>
    </Form>
  );
};

export default QuickSearchBar;
