import React from "react";
import ExploreSpacesSection from "./_components/ExploreSpacesSection";
import { fetchListings } from "@/actions/spaces.action";
import { Pagination } from "@/typescript/types";

type SearchParams = { [key: string]: string | string[] | undefined };

interface ExploreSpacesPageProps {
  searchParams: Promise<SearchParams>;
}

const ExploreSpacesPage = async ({ searchParams }: ExploreSpacesPageProps) => {
  const query = await searchParams;
  const {
    records: listings = [],
    page = 1,
    limit = 10,
    total = 0,
  } = (await fetchListings(query))?.result;
  const pagination: Pagination = {
    page: parseInt(page as string, 10),
    limit: parseInt(limit as string, 10),
    total: parseInt(total as string, 10),
  };
  return <ExploreSpacesSection listings={listings} initialQuery={query} pagination={pagination} />;
};

export default ExploreSpacesPage;
