"use client";

import React from "react";
import Image from "next/image";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { DynamicIcon } from "lucide-react/dynamic";
import { PropertyData } from "@/typescript/interfaces";
import { roomTypeLabels } from "@/constants/constants";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { ImageType } from "@/typescript/types";
import "swiper/css/navigation";
import { Badge } from "../ui/badge";
import Link from "next/link";
import formattedCurrency from "@/lib/currencyFormatter";
import formatDate from "@/utils/formatDate";

interface PropertyListingPopupProps {
  listing: PropertyData;
}

const PropertyListingPopup = ({ listing }: PropertyListingPopupProps) => {
  return (
    <Popover>
      <div className="relative flex flex-col items-center">
        <PopoverTrigger className="bg-secondary px-6 py-2 rounded-sm text-white text-base w-[110px] cursor-pointer">
          {formattedCurrency.format(listing?.monthlyRent) || "N/A"}
        </PopoverTrigger>

        <div className="w-0 h-0 border-l-6 border-r-6 border-t-6 border-l-transparent border-r-transparent border-t-secondary" />
      </div>

      <PopoverContent
        side="top"
        className="relative p-[15px] w-full max-w-[329px] rounded-[16px]"
        sideOffset={10}
      >
        <div
          className="absolute -bottom-2 left-1/2 -translate-x-1/2 w-0 h-0
      border-l-8 border-r-8 border-t-8
      border-l-transparent border-r-transparent border-t-white"
        ></div>

        <Badge className="rounded-full absolute top-6 left-6 z-30">
          {formattedCurrency.format(listing?.monthlyRent) || "N/A"}
        </Badge>

        <div className="space-y-[9px]">
          <ListingImages images={listing?.photos} />
          <Link
            href={`/explore-spaces/${listing._id}`}
            className="flex items-center gap-2 text-[#555555]"
          >
            <DynamicIcon name="map-pin" size={18} color="#2980B9" />
            <h4 className="text-base font-semibold truncate" title={listing?.fullAddress || "N/A"}>
              {listing?.fullAddress || "N/A"}
            </h4>
          </Link>
          <div className="flex justify-between items-center gap-2 text-gray-700 text-sm">
            <p className="text-[14px]">
              Available: {formatDate(listing?.availabilityDate as string) || "N/A"}
            </p>
            <Badge className="p-[10px] text-[14px] bg-primary/10 text-secondary font-normal rounded-[10px]">
              {roomTypeLabels[listing?.spaceType] || "N/A"}
            </Badge>
          </div>
        </div>
      </PopoverContent>
    </Popover>
  );
};

interface ListingImagesProps {
  images: ImageType[];
}

const ListingImages = ({ images }: ListingImagesProps) => {
  return (
    <Swiper className="w-full" navigation={true} modules={[Navigation]}>
      {images.map((image: ImageType, idx: number) => {
        const imageUrl =
          process.env.NEXT_PUBLIC_CDN_URL + "/" + image.filePath + "/" + image.fileName;
        return (
          <SwiperSlide key={idx}>
            <Image
              src={imageUrl}
              alt={`Image ${idx}`}
              width={800}
              height={800}
              priority
              className="w-full h-full object-cover rounded-[10px]"
            />
          </SwiperSlide>
        );
      })}
    </Swiper>
  );
};

export default PropertyListingPopup;
