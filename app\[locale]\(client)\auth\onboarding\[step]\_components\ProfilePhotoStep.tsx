"use client";

import React from "react";
// import FileUpload from "@/components/ui/file-upload";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import FileUpload from "@/components/ui/file-upload-with-crop";

const ProfilePhotoStep = () => {
  const router = useRouter();
  const { currentStep, steps, setUserData, setCurrentStep, markStepCompleted } = useSignupStore();

  const handlePrevious = () => {
    // if (currentStep === 0) {
    //   router.push("/auth/register");
    // }
    setCurrentStep(currentStep - 1);
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (currentStep === steps.length - 1) return;
    markStepCompleted(currentStep);
    setCurrentStep(currentStep + 1);
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  const handleFileChange = async (
    file: File,
    crop: { width: number; height: number; x: number; y: number },
  ) => {
    try {
      const baseURL = process.env.NEXT_PUBLIC_API_URL;
      const formData = new FormData();
      formData.append("file", file);
      formData.append("path", "/user/profile");
      formData.append("crop", "true");
      formData.append("left", crop.x.toString());
      formData.append("top", crop.y.toString());
      formData.append("width", crop.width.toString());
      formData.append("height", crop.height.toString());
      const response = await fetch(`${baseURL}/upload/file`, {
        method: "POST",
        body: formData,
      });
      const data = (await response.json())?.payload;

      setUserData({
        avatar: {
          originalName: data.originalName,
          fileName: data.fileName,
          fileSize: data.fileSize,
          mimeType: data.mimeType,
          filePath: data.filePath,
        },
      });
    } catch (error: unknown) {
      console.error("Error uploading avatar file:", error);
    }

    // const preview = URL.createObjectURL(file);
    // console.log("preview", preview);
    // // setUserData({ avatar: preview });
  };

  return (
    <div>
      <FileUpload onFileChange={handleFileChange} />
      <div className="flex justify-between gap-6 mt-8">
        <Button
          type="button"
          variant="outline"
          onClick={handlePrevious}
          className="flex-1 !py-6 cursor-pointer border-primary"
        >
          Previous
        </Button>
        <Button
          type="submit"
          onClick={handleNext}
          disabled={currentStep === steps.length - 1}
          className="flex-1 !py-6 cursor-pointer"
        >
          {currentStep === steps.length - 1 ? "Complete" : "Continue"}
        </Button>
      </div>
    </div>
  );
};

export default ProfilePhotoStep;
