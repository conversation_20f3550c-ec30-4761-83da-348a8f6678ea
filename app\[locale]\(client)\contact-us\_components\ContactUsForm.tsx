"use client";

import React from "react";
import { useForm } from "react-hook-form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { contactUsFormSchema, ContactUsFormValues } from "@/lib/validations";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { handleContactUs } from "@/actions/contact.action";
import { Card, CardContent } from "@/components/ui/card";
import { useTranslations } from "next-intl";

const ContactUsForm = () => {
  const form = useForm<ContactUsFormValues>({
    resolver: zodResolver(contactUsFormSchema),
    defaultValues: {
      name: "",
      email: "",
      phone: "",
      subject: "",
      message: "",
    },
  });

  const onSubmit = async (data: ContactUsFormValues) => {
    await handleContactUs(data);
  };

  const t = useTranslations("ContactPage");
  const nameLabel = t("formSide.fields.name", { defaultValue: "Name" });
  const emailLabel = t("formSide.fields.email", { defaultValue: "Email" });
  const phoneLabel = t("formSide.fields.phone", { defaultValue: "Phone" });
  const subjectLabel = t("formSide.fields.subject", { defaultValue: "Subject" });
  const messageLabel = t("formSide.fields.message", { defaultValue: "Message" });
  const submitButtonLabel = t("formSide.submitButton", {
    defaultValue: "Send Message",
  });

  return (
    <Card className="bg-transparent border-none shadow-none py-0">
      <CardContent className="px-0">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-5">
            <div className="flex flex-col md:flex-row gap-4">
              <FormField
                name="name"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="input-label">
                      {nameLabel} <span className="field-required" />
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="John Doe"
                        type="text"
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="email"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="input-label">
                      {emailLabel} <span className="field-required" />
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="<EMAIL>"
                        type="email"
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="flex flex-col md:flex-row gap-4">
              <FormField
                name="phone"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="input-label">
                      {phoneLabel} <span className="field-required" />
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="+************"
                        type="tel"
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="subject"
                control={form.control}
                render={({ field }) => (
                  <FormItem className="w-full">
                    <FormLabel className="input-label">
                      {subjectLabel}
                      <span className="field-required" />
                    </FormLabel>
                    <FormControl>
                      <Input
                        {...field}
                        placeholder="Your Subject"
                        type="text"
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <FormField
              name="message"
              control={form.control}
              render={({ field }) => (
                <FormItem className="w-full">
                  <FormLabel className="input-label">
                    {messageLabel} <span className="field-required" />
                  </FormLabel>
                  <FormControl>
                    <Textarea {...field} placeholder="Message" className="min-h-44 bg-white" />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button className="w-full h-auto py-4 rounded-full cursor-pointer text-lg">
              {submitButtonLabel}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default ContactUsForm;
