"use client";

import React from "react";
import Link from "next/link";

import { StepItem } from "@/typescript/interfaces";
import clsx from "clsx";
import { useSignupStore } from "@/store/userSignUpStore";
import { DynamicIcon } from "lucide-react/dynamic";

interface Args {
  steps: StepItem[];
}

const SidebarStep = ({ steps }: Args) => {
  const { currentStep, completedSteps } = useSignupStore();

  return (
    <aside className="w-full border-r p-6 pl-[36px] py-[50px] space-y-10 bg-white border rounded-xl">
      {steps.map((step, index) => {
        const isActive = index === currentStep;
        const isCompleted = completedSteps.includes(index);
        const isFirst = index === 0;
        const isLast = index === steps.length - 1;

        return (
          <Link
            href={`/dashboard/my-spaces/add/${index}`}
            key={index}
            className="flex items-center gap-3 relative"
          >
            {/* Connector Lines */}
            {!isFirst && (
              <div
                className={clsx(
                  "absolute left-[22px] top-[-40px] h-10 w-px",
                  isCompleted ? "bg-primary" : "bg-gray-200",
                )}
              />
            )}
            {!isLast && (
              <div
                className={clsx(
                  "absolute left-[22px] bottom-[-40px] h-10 w-px",
                  isCompleted ? "bg-primary" : "bg-gray-200",
                )}
              />
            )}

            {/* Step Circle */}
            <div
              className={clsx(
                "relative z-10 flex items-center justify-center w-[44px] h-[44px] rounded-full border-2 text-gray-400",
                isActive
                  ? "bg-gray-200 border-gray-200 text-[#B4B4B4]"
                  : isCompleted
                    ? "bg-primary border-primary text-white"
                    : "bg-gray-200 text-[#B4B4B4] border-gray-200",
              )}
            >
              <DynamicIcon name={step.icon} size={24} />
            </div>

            {/* Label */}
            <span
              className={clsx(
                "text-base",
                isActive
                  ? "text-gray-700 font-normal"
                  : isCompleted
                    ? "text-primary"
                    : "text-gray-500",
              )}
            >
              {step.label}
            </span>
          </Link>
        );
      })}
    </aside>
  );
};

export default SidebarStep;
