import { getListingSteps } from "@/lib/getOnboardingSteps";
import isAuthenticated from "@/utils/isAuthenticated";
import React from "react";
import SidebarStep from "./_components/SidebarStep";

interface Args {
  children: React.ReactNode;
}

const AddMySpaceLayout = async ({ children }: Args) => {
  const user = (await isAuthenticated({ noRedirect: true }))?.user;
  const steps = getListingSteps(user?.intent, user?.spaceType);
  return (
    <section>
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 lg:col-span-4">
            <SidebarStep steps={steps} />
          </div>
          <div className="col-span-12 lg:col-span-8">{children}</div>
        </div>
      </div>
    </section>
  );
};

export default AddMySpaceLayout;
