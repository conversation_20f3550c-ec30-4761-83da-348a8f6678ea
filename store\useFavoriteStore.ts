import toast from "react-hot-toast";
import { create } from "zustand";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

interface FavoriteState {
  favorites: any[];
  isLoading: boolean;
  getFavorites: () => Promise<void>;
  addToFavorite: (listingId: string) => Promise<void>;
  removeFromFavorite: (listingId: string) => Promise<void>;
  failed: boolean;
}

const useFavoriteStore = create<FavoriteState>()((set, get) => ({
  favorites: [],
  isLoading: false,
  failed: false,

  getFavorites: async () => {
    set({ isLoading: true });
    try {
      const token = document.cookie
        .split("; ")
        .find((row) => row.startsWith("token="))
        ?.split("=")[1];

      const response = await fetch(`${baseURL}/web/user/me/favorite-spaces`, {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      const data = (await response.json())?.result?.records;

      const spaces = data?.map((fav: any) => fav?.space) || [];
      set({ favorites: spaces, isLoading: false });
    } catch (error) {
      console.error("getFavorites error ❌", error);
      set({ isLoading: false });
    }
  },

  addToFavorite: async (listingId: string) => {
    const current = get().favorites;
    const alreadyExists = current.some((item) => item._id === listingId);
    if (alreadyExists) return;

    set({ isLoading: true });

    try {
      const token = document.cookie
        .split("; ")
        .find((row) => row.startsWith("token="))
        ?.split("=")[1];

      const response = await fetch(`${baseURL}/web/space/${listingId}/favorite`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      const data = await response.json();
      const space = data?.space;

      if (space && !alreadyExists) {
        set({ favorites: [...current, space], isLoading: false });
        toast.success(data?.message);
        set({ failed: false });
      } else {
        set({ isLoading: false });
        toast.error("Failed to add to favorites, Please login to continue");
        set({ failed: true });
      }
    } catch (error) {
      console.error("addToFavorite error ❌", error);
      set({ isLoading: false });
    }
  },

  removeFromFavorite: async (listingId: string) => {
    set({ isLoading: true });

    try {
      const token = document.cookie
        .split("; ")
        .find((row) => row.startsWith("token="))
        ?.split("=")[1];

      const response = await fetch(`${baseURL}/web/space/${listingId}/favorite`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
      });

      if (!response.ok) throw new Error("Failed to remove favorite");

      const data = await response.json();

      const updated = get().favorites.filter((item) => item._id !== listingId);
      set({ favorites: updated, isLoading: false });
      toast.success(data?.message);
      set({ failed: false });
    } catch (error) {
      console.error("removeFromFavorite error ❌", error);
      toast.error("Failed to remove from favorites, Please login to continue");
      set({ isLoading: false });
      set({ failed: true });
    }
  },
}));

export default useFavoriteStore;
