import { PropertyData, StepItem, UserProfile } from "@/typescript/interfaces";
import { create } from "zustand";
import { persist } from "zustand/middleware";

// User profile data

// Property data

// Combined data structure
interface SignupData {
  user: UserProfile;
  property: PropertyData;
}

interface SignupStore {
  data: SignupData;
  setUserData: (values: Partial<UserProfile>) => void;
  setPropertyData: (values: Partial<PropertyData>) => void;
  clear: () => void;

  steps: StepItem[];
  setSteps: (steps: StepItem[]) => void;

  currentStep: number;
  setCurrentStep: (step: number) => void;

  completedSteps: number[];
  markStepCompleted: (stepIndex: number) => void;
  resetCompletedSteps: () => void;
}

// Default values
const defaultUserData: UserProfile = {
  firstName: "",
  lastName: "",
  email: "",
  intent: "",
  spaceType: "",
  genderIdentity: "",
  sexualOrientation: "",
  age: 0,
  smokeCigarettes: "",
  smokeMarijuana: "",
  workFromHome: "",
  travel: "",
  cleanliness: "",
  describeMyselfAs: [],
  zodiac: "",
  selfDescription: "",
  fluentLanguages: [],
  avatar: {
    originalName: "",
    fileName: "",
    fileSize: 0,
    mimeType: "",
    filePath: "",
  },
  preferredLocation: [0, 0],
  preferredLocationLabel: "",
  rentalStartDate: "",
  rentalDuration: 0,
  willingToSignRentalAgreement: "",
  wantFurnished: "",
  bathroom: "",
  bedroomSize: "",
  bedrooms: "",
  bathrooms: "",
  maxMonthlyBudget: 0,
  pets: "",
  parkingRequired: "",
  preferredGenderIdentity: [],
  preferredSexualOrientation: [],
  preferredAgeRange: [],
  preferredSmokingHabits: [],
  idealRoommateDescription: "",
  idealTenantDescription: "",
  isGoogleConnected: false,
  isFacebookConnected: false,
};

const defaultPropertyData: PropertyData = {
  spaceType: "",
  fullAddress: "",
  residenceType: "",
  size: "",
  bedrooms: "",
  bathrooms: "",
  ownerOccupied: "",
  numPeopleInHome: "",
  amenities: [],
  parking: [],
  neighborhood: [],
  currentPets: [],
  allowedPets: [],
  smokeCigarettes: "",
  smokeMarijuana: "",
  overnightGuestsAllowed: "",
  homeDescription: "",
  availabilityDate: "",
  availabilityDuration: 1,
  minimumDuration: 1,
  monthlyRent: 0,
  depositAmount: 0,
  leaseRequired: "",
  requiredReferences: "",
  utilitiesIncluded: "",
  furnished: "",
  bedroomSize: "",
  brightness: "",
  bathroom: "",
  roomFeatures: [],
  spacePhotos: [],
  preferredGenderIdentity: [],
  preferredSexualOrientation: [],
  preferredAgeRange: [],
  idealRoommateDescription: "",
  idealTenantDescription: "",
  rentalStartDate: "",
  rentalDuration: 0,
  maxMonthlyBudget: 0,
  willingToSignRentalAgreement: "",
  wantFurnished: "",
  pets: "",
  parkingRequired: "",
  preferredSmokingHabits: [],
  preferredRoommateGenderIdentity: [],
  preferredRoommateSexualOrientation: [],
  preferredRoommateAgeRange: [],
  preferredRoommateSmokingHabits: [],
  photos: [],
  location: [0, 0],
  locationLabel: "",
};

export const useSignupStore = create<SignupStore>()(
  persist(
    (set) => ({
      data: {
        user: defaultUserData,
        property: defaultPropertyData,
      },

      setUserData: (values) =>
        set((state) => ({
          data: {
            ...state.data,
            user: { ...state.data.user, ...values },
          },
        })),

      setPropertyData: (values) =>
        set((state) => ({
          data: {
            ...state.data,
            property: { ...state.data.property, ...values },
          },
        })),

      clear: () =>
        set({
          data: {
            user: defaultUserData,
            property: defaultPropertyData,
          },
        }),

      steps: [],
      setSteps: (steps) => set({ steps }),

      currentStep: 0,
      setCurrentStep: (step) => set({ currentStep: step }),

      completedSteps: [],
      markStepCompleted: (stepIndex) =>
        set((state) => ({
          completedSteps: [...new Set([...state.completedSteps, stepIndex])],
        })),
      resetCompletedSteps: () => set({ completedSteps: [] }),
    }),
    {
      name: "signup-store",
    },
  ),
);
