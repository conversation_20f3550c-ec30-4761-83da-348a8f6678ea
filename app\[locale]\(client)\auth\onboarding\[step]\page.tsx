"use client";

import React, { useEffect } from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { usePara<PERSON>, useRouter } from "next/navigation";
import {
  ABOUT_YOU,
  RENTAL_BASICS,
  RENTAL_PREFERENCES,
  ROOMMATE_PREFERENCES,
  TENANT_PREFERENCES,
  THE_HOME,
  THE_ROOM,
  THE_SPACE,
  UPLOAD_PROFILE_PHOTO,
  UPLOAD_SPACE_PHOTOS,
} from "@/constants/auth.constants";
import AboutYouStep from "./_components/AboutYouStep";
import ProfilePhotoStep from "./_components/ProfilePhotoStep";
import HomeStep from "./_components/HomeStep";
import RoomStep from "./_components/RoomStep";
import SpaceStep from "./_components/SpaceStep";
import SpacePhotosStep from "./_components/SpacePhotosStep";
import RoommatePreferencesStep from "./_components/RoommatePreferencesStep";
import RentalBasicsStep from "./_components/RentalBasicsStep";
import TenantPreferencesStep from "./_components/TenantPreferencesStep";
import RentalPreferencesStep from "./_components/RentalPreferencesStep";
import AboutYouEntireStep from "./_components/AboutYouEntireStep";
import AboutYouFindStep from "./_components/AboutYouFindStep";
import FindPrivateRoomStep from "./_components/FindPrivateRoomStep";
import FindRoommatePreferencesStep from "./_components/FindRoommatePreferecesStep";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

const OnboardingPage = () => {
  const { step } = useParams();
  const router = useRouter();
  const currentStep = parseInt(step as string);
  const { data, steps, setCurrentStep, currentStep: storeCurrentStep } = useSignupStore();

  useEffect(() => {
    if (!steps.length) {
      router.push("/auth/register");
      return;
    }

    // If user is trying to access a step beyond their current progress,
    // redirect them to their current step to prevent skipping steps
    // if (currentStep > storeCurrentStep && storeCurrentStep >= 0) {
    //   router.push(`/auth/onboarding/${storeCurrentStep}`);
    //   return;
    // }

    // Update the store's current step to match the URL
    setCurrentStep(currentStep);
  }, [currentStep, storeCurrentStep, steps.length, router, setCurrentStep]);

  useEffect(() => {
    if (typeof storeCurrentStep === "number" && storeCurrentStep < 0) {
      router.replace("/auth/register");
    }
  }, [storeCurrentStep, router]);

  const stepItem = steps[currentStep];
  const stepLabel = stepItem?.label;

  // Map step names to components
  const renderStepForm = () => {
    switch (stepLabel) {
      case ABOUT_YOU:
        return data.user.intent === "rent" ? (
          data.user.spaceType === "private_room" ? (
            <AboutYouStep />
          ) : (
            <AboutYouEntireStep />
          )
        ) : (
          <AboutYouFindStep />
        );
      case UPLOAD_PROFILE_PHOTO:
        return <ProfilePhotoStep />;
      case THE_HOME:
        return <HomeStep />;
      case THE_ROOM:
        return data.user.intent === "rent" ? (
          data.user.spaceType === "private_room" ? (
            <RoomStep />
          ) : (
            <p>Entire Room Step</p>
          )
        ) : (
          <FindPrivateRoomStep />
        );
      case THE_SPACE:
        return <SpaceStep />;
      case UPLOAD_SPACE_PHOTOS:
        return <SpacePhotosStep />;
      case ROOMMATE_PREFERENCES:
        return data.user.intent === "rent" ? (
          data.user.spaceType === "private_room" ? (
            <RoommatePreferencesStep />
          ) : (
            <p>Entire Roommate Preferences Step</p>
          )
        ) : (
          <FindRoommatePreferencesStep />
        );
      case RENTAL_BASICS:
        return <RentalBasicsStep />;
      case TENANT_PREFERENCES:
        return <TenantPreferencesStep />;
      case RENTAL_PREFERENCES:
        return <RentalPreferencesStep />;
      default:
        return <div>Form not found for step: {stepLabel}</div>;
    }
  };

  return (
    <div>
      <Card>
        <CardHeader className="px-6 lg:px-10">
          <CardTitle className="text-[27px] text-secondary font-semibold flex justify-center items-center gap-2.5 mt-[27px]">
            {stepItem?.label}
          </CardTitle>
        </CardHeader>
        <CardContent className="px-6 lg:px-[87px] pb-[30px]">{renderStepForm()}</CardContent>
      </Card>
    </div>
  );
};

export default OnboardingPage;
