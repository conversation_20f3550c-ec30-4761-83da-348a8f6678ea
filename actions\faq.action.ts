import { FAQ } from "@/typescript/types";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

// Get all faqs
export const getFaqs = async (): Promise<{
  message: string;
  status?: number;
  faqs: FAQ[];
}> => {
  try {
    const res = await fetch(`${baseURL}/web/faq`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return res.json();
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
        faqs: [],
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
        faqs: [],
      };
    }
  }
};
