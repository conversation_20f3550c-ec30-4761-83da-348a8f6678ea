"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import {
  ageRanges,
  preferredGenders,
  preferredSexualOrientations,
  smokingHabits,
} from "@/constants/constants";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Button } from "@/components/ui/button";
import { RoommatePrefsData, roommatePrefsSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
// import { handleSignup } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { TFormMode } from "@/typescript/types";
import { PropertyData } from "@/typescript/interfaces";
import { MultiSelect } from "@/components/ui/MultiSelect";

interface Args {
  mode?: TFormMode;
  listing?: PropertyData;
}

const RoommatePreferencesStep = ({ mode, listing }: Args) => {
  const router = useRouter();
  const {
    currentStep,
    steps,
    setPropertyData,
    data,
    setCurrentStep,
    clear,
    setUserData,
    markStepCompleted,
    resetCompletedSteps,
  } = useSignupStore();
  const form = useForm<RoommatePrefsData>({
    resolver: zodResolver(roommatePrefsSchema),
    defaultValues: {
      preferredGenderIdentity:
        listing?.preferredGenderIdentity || data.property.preferredGenderIdentity || [],
      preferredSexualOrientation:
        listing?.preferredSexualOrientation || data.property.preferredSexualOrientation || [],
      preferredAgeRange: listing?.preferredAgeRange || data.property.preferredAgeRange || [],
      preferredSmokingHabits:
        listing?.preferredSmokingHabits || data.property.preferredSmokingHabits || [],
      idealRoommateDescription:
        listing?.idealRoommateDescription || data.property.idealRoommateDescription || "",
    },
  });

  const locationURL =
    mode === "add" ? "/dashboard/my-spaces/add" : `/dashboard/my-spaces/edit/${listing?._id}`;

  async function onSubmit(values: RoommatePrefsData) {
    setPropertyData(values);
    setUserData({
      preferredAgeRange: values.preferredAgeRange,
      preferredGenderIdentity: values.preferredGenderIdentity,
      preferredSexualOrientation: values.preferredSexualOrientation,
    });
    // const isLastStep = currentStep === steps.length - 1;
    // if (isLastStep) {
    //   router.push("/");
    //   setCurrentStep(0);
    //   clear();
    // } else {
    //   router.push(`/auth/onboarding/${currentStep + 1}`);
    // }
    const fullDataToSend = {
      property: {
        ...data.property,
        ...values,
        spaceType: data.user.spaceType,
      },
    };
    let res;
    if (mode === "add") {
      res = await createListing(fullDataToSend.property);
    } else {
      res = await updateListing(listing?._id as string, fullDataToSend.property);
    }
    if (res.status >= 200 && res.status < 300) {
      toast.success(res.message);
      router.push("/");
      markStepCompleted(currentStep);
      setCurrentStep(0);
      resetCompletedSteps();
      clear();
    } else {
      toast.error(res.message);
      router.push("/");
      markStepCompleted(currentStep);
      setCurrentStep(0);
      resetCompletedSteps();
      clear();
    }
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`${locationURL}/${currentStep - 1}`);
  };

  // const handleNext = () => {
  //   // Check schema is valid
  //   if (!form.formState.isValid) return;
  //   if (currentStep === steps.length - 1) return;
  //   setCurrentStep(currentStep + 1);
  //   router.push(`${locationURL}/${currentStep + 1}`);
  // };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="preferredGenderIdentity"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Gender Identity<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={preferredGenders} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredSexualOrientation"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Sexual Orientation<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={preferredSexualOrientations} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredAgeRange"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Age Range<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={ageRanges} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredSmokingHabits"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Smoking Habits<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  defaultChecked
                  options={smokingHabits}
                  onValueChange={field.onChange}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="idealRoommateDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Describe Your Ideal Roommate<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Ideal roommate description" {...field} className="h-40" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Complete" : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

const createListing = async (data: any) => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;
  const token = document.cookie
    .split("; ")
    .find((row) => row.startsWith("token="))
    ?.split("=")[1];

  try {
    const response = await fetch(`${baseURL}/web/space`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const resData = await response.json();
      return {
        message: resData.message,
        status: response.status,
      };
    } else {
      return {
        message: "An error occurred",
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

const updateListing = async (id: string, data: any) => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;
  const token = document.cookie
    .split("; ")
    .find((row) => row.startsWith("token="))
    ?.split("=")[1];

  try {
    const response = await fetch(`${baseURL}/web/space/${id}`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const resData = await response.json();
      return {
        message: resData.message,
        status: response.status,
      };
    } else {
      return {
        message: "An error occurred",
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export default RoommatePreferencesStep;
