"use client";

import React, { useEffect } from "react";
import { UserProfile } from "@/typescript/interfaces";
import { authStore } from "@/store/authStore";

interface StateInitializerProps {
  user: UserProfile;
  children: React.ReactNode;
}

const StateInitializer = ({ user, children }: StateInitializerProps) => {
  useEffect(() => {
    if (user) {
      authStore.setState({ user, isAuthenticated: true });
    } else {
      authStore.setState({ user: null, isAuthenticated: false });
    }
  }, [user]);

  return children;
};

export default StateInitializer;
