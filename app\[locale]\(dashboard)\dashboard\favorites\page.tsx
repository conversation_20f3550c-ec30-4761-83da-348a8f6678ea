"use client";

import React, { useEffect } from "react";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import FavoriteCard from "@/components/Cards/FavoriteCard/FavoriteCard";
import useFavoriteStore from "@/store/useFavoriteStore";

const FavoriesPage = () => {
  const { favorites, getFavorites } = useFavoriteStore();

  useEffect(() => {
    getFavorites();
  }, [getFavorites]);

  return (
    <React.Fragment>
      <TopBanner title="My Favorites" />
      <section className="py-[40px]">
        <div className="container">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-5">
            {favorites.length > 0 ? (
              favorites.map((favorite) => <FavoriteCard key={favorite._id} listing={favorite} />)
            ) : (
              <p>No Favorites Found</p>
            )}
          </div>
        </div>
      </section>
    </React.Fragment>
  );
};

export default FavoriesPage;
