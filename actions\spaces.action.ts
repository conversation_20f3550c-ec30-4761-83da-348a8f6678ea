const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const fetchListings = async (query?: any) => {
  try {
    const res = await fetch(`${baseURL}/web/space/paginate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(query),
    });
    return res.json();
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const getListingById = async (id: string) => {
  try {
    const res = await fetch(`${baseURL}/web/space/${id}`, {
      method: "GET",
    });
    return res.json();
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const handleListingDelete = async (id: string) => {
  try {
    const token = document.cookie
      .split("; ")
      .find((row) => row.startsWith("token="))
      ?.split("=")[1];
    const response = await fetch(`${baseURL}/web/space/${id}`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    const resData = await response.json();

    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};
