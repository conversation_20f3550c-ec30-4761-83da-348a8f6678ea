import React from "react";
import ExploreFacesSection from "./_components/ExploreFacesSection";
import { getAllFaces } from "@/actions/faces.action";

// type SearchParams = { [key: string]: string | string[] | undefined };

const ExploreFacesPage = async () => {
  const users = (await getAllFaces())?.result?.records || [];
  return <ExploreFacesSection initialUsers={users} />;
};

export default ExploreFacesPage;
