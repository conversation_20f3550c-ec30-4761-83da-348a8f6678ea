import { z } from "zod";

export const aboutYouSchema = z.object({
  genderIdentity: z.string(),
  sexualOrientation: z.string(),
  age: z.coerce.number().optional(),
  smokeCigarettes: z.string().optional(),
  smokeMarijuana: z.string().optional(),
  workFromHome: z.string().optional(),
  travel: z.string().optional(),
  cleanliness: z.string().optional(),
  fluentLanguages: z.array(z.string()).optional(),
  describeMyselfAs: z.array(z.string()),
  zodiac: z.string().optional(),
  selfDescription: z.string().min(1),
});

export const theRoomSchema = z.object({
  preferredLocation: z.array(z.number(), z.number()),
  preferredLocationLabel: z.string(),
  rentalStartDate: z.string(),
  // rentalDuration: z.coerce.number(),
  maxMonthlyBudget: z.coerce.number(),
  willingToSignRentalAgreement: z.string(),
  wantFurnished: z.string(),
  bedroomSize: z.string(),
  bathroom: z.string(),
  pets: z.string(),
  parkingRequired: z.string(),
});

export const roommatePrefsSchema = z.object({
  preferredGenderIdentity: z.array(z.string()),
  preferredSexualOrientation: z.array(z.string()),
  preferredAgeRange: z.array(z.string()),
  preferredSmokingHabits: z.array(z.string()),
});

export const rentalPrefsSchema = z.object({
  preferredLocation: z.array(z.number(), z.number()),
  preferredLocationLabel: z.string(),
  rentalStartDate: z.string(),
  // rentalDuration: z.coerce.number(),
  maxMonthlyBudget: z.coerce.number(),
  willingToSignRentalAgreement: z.string(),
  wantFurnished: z.string(),
  bedroomSize: z.string(),
  bathRoom: z.string(),
  pets: z.string(),
  parkingRequired: z.string(),
});

export const guestPrefsSchema = z.object({
  idealTenantDescription: z.string().min(20),
});

export type AboutYouData = z.infer<typeof aboutYouSchema>;
export type TheRoomData = z.infer<typeof theRoomSchema>;
export type RoommatePrefsData = z.infer<typeof roommatePrefsSchema>;
export type RentalPrefsData = z.infer<typeof rentalPrefsSchema>;
export type GuestPrefsData = z.infer<typeof guestPrefsSchema>;
