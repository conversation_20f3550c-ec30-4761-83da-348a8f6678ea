"use client";

import { useState, useCallback } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { X } from "lucide-react";
import <PERSON>ropper from "react-easy-crop";
import {
  <PERSON><PERSON>,
  DialogContent,
  Di<PERSON>Header,
  <PERSON><PERSON>Title,
  DialogFooter,
} from "@/components/ui/dialog";
import { AiOutlineCloudUpload } from "react-icons/ai";

import getCroppedImg from "@/lib/cropImage"; // Helper to get cropped image
import { cn } from "@/lib/utils";
import Image from "next/image";

interface FileUploadProps {
  onFileChange?: (file: File, crop: any) => void;
  onMultipleFilesChange?: (files: File[]) => void;
  multiple?: boolean;
  maxFiles?: number;
}

export default function FileUpload({
  onFileChange,
  onMultipleFilesChange,
  multiple = false,
  maxFiles = 5,
}: FileUploadProps) {
  const [files, setFiles] = useState<File[]>([]);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [cropModalOpen, setCropModalOpen] = useState(false);
  const [crop, setCrop] = useState({ x: 0, y: 0 });
  const [zoom, setZoom] = useState(1);
  const [croppedAreaPixels, setCroppedAreaPixels] = useState<any>(null);
  const [imageSrc, setImageSrc] = useState<string | null>(null);
  const [isDragging, setIsDragging] = useState(false);

  const onCropComplete = useCallback(
    (_: any, croppedAreaPixels: { width: number; height: number; x: number; y: number }) => {
      setCroppedAreaPixels(croppedAreaPixels);
    },
    [],
  );

  const handleDrop = (e: React.DragEvent<HTMLLabelElement>) => {
    e.preventDefault();
    e.stopPropagation();
    setIsDragging(false);

    const file = e.dataTransfer.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => {
        setImageSrc(reader.result as string);
        setSelectedFile(file);
        setCropModalOpen(true);
      };
      reader.readAsDataURL(file);
    }
  };

  const handleFileChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    const reader = new FileReader();
    reader.onload = () => {
      setImageSrc(reader.result as string);
      setSelectedFile(file);
      setCropModalOpen(true);
    };
    reader.readAsDataURL(file);
  };

  const handleCropConfirm = async () => {
    if (!imageSrc || !croppedAreaPixels || !selectedFile) return;

    const croppedBlob = await getCroppedImg(imageSrc, croppedAreaPixels);
    const croppedFile = new File([croppedBlob], selectedFile.name, {
      type: selectedFile.type,
    });

    const updatedFiles = multiple ? [...files, croppedFile].slice(0, maxFiles) : [croppedFile];

    setFiles(updatedFiles);
    onMultipleFilesChange?.(updatedFiles);
    onFileChange?.(selectedFile, croppedAreaPixels);
    setCropModalOpen(false);
  };

  const removeFile = (index: number) => {
    const updatedFiles = [...files];
    updatedFiles.splice(index, 1);
    setFiles(updatedFiles);

    if (multiple && onMultipleFilesChange) onMultipleFilesChange(updatedFiles);
    if (updatedFiles.length > 0) onFileChange?.(updatedFiles[0], croppedAreaPixels);
  };

  return (
    <Card className="border-none shadow-none">
      <CardContent>
        <form>
          {files.length > 0 ? null : (
            <label
              htmlFor="dropzone-file"
              onDrop={handleDrop}
              onDragOver={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsDragging(true);
              }}
              onDragLeave={(e) => {
                e.preventDefault();
                e.stopPropagation();
                setIsDragging(false);
              }}
              className={cn(
                "flex flex-col items-center justify-center w-full h-64 border-[0.5px] border-dashed rounded-lg cursor-pointer bg-white dark:bg-gray-700 hover:bg-gray-100 transition p-6",
                isDragging ? "border-blue-500 bg-blue-50 dark:bg-blue-900" : "border-[#8E44AD]",
              )}
            >
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                {/* <UploadIcon className="w-10 h-10 text-gray-400" /> */}
                <AiOutlineCloudUpload size={60} color="#1C7BBA" />
                <p className="mt-[20px] text-base lg:text-[18px] text-[#555555] text-center">
                  Drag and Drop or Click to Upload Your Profile Photo
                </p>
              </div>
              <input
                id="dropzone-file"
                type="file"
                className="hidden"
                onChange={handleFileChange}
                accept="image/*"
              />
            </label>
          )}

          {files.length > 0 && (
            <div className="grid gap-2">
              {files.map((file, index) => {
                const prevURL = URL.createObjectURL(file);
                return (
                  <div key={index} className="p-2 border rounded relative w-[300px] mx-auto">
                    <Image
                      src={prevURL}
                      alt={file.name}
                      width={500}
                      height={500}
                      className="w-[300px] h-full object-cover rounded-lg"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeFile(index)}
                      type="button"
                      className="absolute top-2 right-2"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })}
            </div>
          )}
        </form>
      </CardContent>

      {/* Crop Modal */}
      {cropModalOpen && imageSrc && (
        <Dialog open={cropModalOpen} onOpenChange={setCropModalOpen}>
          <DialogContent className="max-w-3xl rounded-2xl">
            <DialogHeader>
              <DialogTitle>Crop Image</DialogTitle>
            </DialogHeader>

            <div className="relative w-full h-[400px] bg-black rounded-md overflow-hidden">
              <Cropper
                image={imageSrc!}
                crop={crop}
                zoom={zoom}
                aspect={10 / 10}
                onCropChange={setCrop}
                onZoomChange={setZoom}
                onCropComplete={onCropComplete}
              />
            </div>

            <DialogFooter className="flex flex-col! gap-4">
              <div>
                <div className="flex flex-col items-start gap-2">
                  <label htmlFor="zoom">Zoom:</label>
                  <input
                    id="zoom"
                    type="range"
                    min={1}
                    max={10}
                    step={1}
                    value={zoom}
                    onChange={(e) => setZoom(Number(e.target.value))}
                    className="w-full"
                  />
                </div>
              </div>
              <div className="flex justify-between gap-4">
                <Button variant="outline" onClick={() => setCropModalOpen(false)}>
                  Cancel
                </Button>
                <Button onClick={handleCropConfirm}>Crop & Upload</Button>
              </div>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      )}
    </Card>
  );
}
