"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import { UserProfile } from "@/typescript/interfaces";
import TheRoomForm from "./TheRoomForm";
import {
  allowedPetsLabels,
  bedRoomsizeLabel,
  furnishedLabels,
  parkingRequiredLabel,
  rentalAggreementLabel,
} from "@/constants/listing.constants";
import formatDate from "@/utils/formatDate";
// import {
//   bathRoomLabels,
//   bedRoomsizeLabel,
//   brightnessLabels,
//   furnishedLabels,
//   roomFeaturesLabels,
// } from "@/constants/listing.constants";

interface Args {
  user?: UserProfile;
}

const TheRoomSection = ({ user }: Args) => {
  const [isEdit, setIsEdit] = useState<boolean>(false);
  return isEdit ? (
    <TheRoomForm isEdit={isEdit} setIsEdit={setIsEdit} user={user as UserProfile} />
  ) : (
    <div className="space-y-7 bg-white shadow p-4 rounded-md border">
      <div className="space-y-3 relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-2xl font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="house-plug" size={25} /> The Room
        </h2>
        <div className="space-y-5">
          <div className="space-y-3">
            <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">Basics</h2>
            <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Preferred Location: {user?.preferredLocationLabel || "N/A"}
              </li>
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Sign Rental Agreement:{" "}
                {rentalAggreementLabel[user?.willingToSignRentalAgreement as string] || "N/A"}
              </li>
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Rental Start Date: {formatDate(user?.rentalStartDate as string) || "N/A"}
              </li>
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Furnished: {furnishedLabels[user?.wantFurnished as string] || "N/A"}
              </li>
              {/* <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Rental Duration: {user?.rentalDuration || "N/A"}
              </li> */}
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Parking Required: {parkingRequiredLabel[user?.parkingRequired as string] || "N/A"}
              </li>
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Max monthly budget: {`$ ${user?.maxMonthlyBudget}` || "N/A"}
              </li>
            </ul>
          </div>
          <div className="space-y-3">
            <h2 className="text-xl font-bold text-primary flex items-center gap-2.5">Room Size</h2>
            <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                {bedRoomsizeLabel[user?.bedroomSize as string] || "N/A"}
              </li>
            </ul>
          </div>
          <div className="space-y-3">
            <h2 className="text-xl font-bold text-primary flex items-center gap-2.5">Bathroom</h2>
            <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Master
              </li>
            </ul>
          </div>
          <div className="space-y-3">
            <h2 className="text-xl font-bold text-primary flex items-center gap-2.5">Pets</h2>
            <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                {allowedPetsLabels[user?.pets as string] || "N/A"}
              </li>
            </ul>
          </div>
        </div>
      </div>
    </div>
  );
};

export default TheRoomSection;
