import type { Metada<PERSON> } from "next";
import { Poppins } from "next/font/google";
import { Toaster } from "react-hot-toast";
import "swiper/css";
import "./globals.css";
import isAuthenticated from "@/utils/isAuthenticated";
import StateInitializer from "@/components/StateInitializer";
import ClientWrapper from "@/components/ClientWrapper";

const poppins = Poppins({
  subsets: ["latin"],
  display: "swap",
  variable: "--font-poppins",
  weight: ["100", "200", "300", "400", "500", "600", "700", "800", "900"],
});

export const metadata: Metadata = {
  title: "gayroom8",
  description: "gayroom8 is a rental spaces platform for LGBTQ+ people.",
};

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode;
}>) {
  const data = await isAuthenticated({ noRedirect: true });

  return (
    <html lang="en">
      <body className={`${poppins.variable} antialiased`}>
        <ClientWrapper>
          <StateInitializer user={data?.user}>{children}</StateInitializer>
          <Toaster position="bottom-right" />
        </ClientWrapper>
      </body>
    </html>
  );
}
