import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";

const FavoriteCardAction = () => {
  return (
    <div className="flex items-center gap-2">
      <Button size={"icon"} variant={"outline"} className="border-primary">
        <DynamicIcon name="square-pen" size={20} />
      </Button>
      <Button size={"icon"} variant={"outline"} className="border-primary">
        <DynamicIcon name="trash-2" size={20} />
      </Button>
    </div>
  );
};

export default FavoriteCardAction;
