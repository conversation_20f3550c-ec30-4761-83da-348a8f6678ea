import React from "react";
import Image from "next/image";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import { FaArrowCircleRight } from "react-icons/fa";
import { getTranslations } from "next-intl/server";

const AboutUsPage = async () => {
  const t = await getTranslations("AboutPage");
  const title = t("title");
  const founder = t.raw("founder");
  const description = t.raw("description");
  return (
    <>
      <TopBanner title={title} />
      <section className="py-[60px]">
        <div className="container">
          <div className="grid grid-cols-12 gap-6 items-center">
            <div className="col-span-12 lg:col-span-5 xl:col-span-4">
              <Image
                src={"/images/about/about.webp"}
                alt="About Us"
                width={1000}
                height={1000}
                priority
                className="w-full lg:max-w-[407px] h-auto lg:h-[424px]"
              />
            </div>
            <div className="col-span-12 lg:col-span-7 xl:col-span-8 space-y-7">
              <p className="text-xl text-primary font-medium flex gap-4 items-center tracking-widest">
                <FaArrowCircleRight size={20} /> {founder.description}
              </p>
              <h2 className="text-3xl lg:text-4xl font-semibold text-secondary">{founder.name}</h2>
              <div className="text-lg lg:text-[22px] text-gray-[#383434] leading-relaxed">
                <p className="before:content-['❝'] before:text-secondary after:content-['❞'] after:text-secondary">
                  {founder.bio}
                </p>
                <p>–H. Jackson Brown, Jr.</p>
              </div>
            </div>
          </div>
          <div className="space-y-5 mt-6 text-lg lg:text-[22px] text-gray-[#383434] leading-relaxed">
            {description.map((para: string, index: number) => (
              <p key={index}>{para}</p>
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

export default AboutUsPage;
