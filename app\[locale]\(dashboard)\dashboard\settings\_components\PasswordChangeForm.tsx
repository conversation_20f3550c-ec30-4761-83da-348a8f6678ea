"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { PasswordChangeData, passwordChangeSchema } from "@/lib/validations/settings";
import { handleUpdatePassword } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { useCookies } from "react-cookie";

const PasswordChangeForm = () => {
  const [cookies] = useCookies(["token"]);
  const form = useForm<PasswordChangeData>({
    resolver: zod<PERSON><PERSON>olver(passwordChangeSchema),
    defaultValues: {
      oldPassword: "",
      newPassword: "",
      confirmPassword: "",
    },
  });

  const onSubmit = async (data: PasswordChangeData) => {
    const token = cookies.token;
    const modifiedData = {
      oldPassword: data.oldPassword,
      newPassword: data.newPassword,
    };
    const response = await handleUpdatePassword(modifiedData, token);
    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message);
    } else {
      toast.error(response.message);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl text-secondary font-medium flex items-center gap-2.5">
          <DynamicIcon name="rotate-ccw-key" size={24} /> Update Password
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              <FormField
                name="oldPassword"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">Old Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Your Old Password"
                        {...field}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="newPassword"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">New Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Your New Password"
                        {...field}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="confirmPassword"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">Confirm Password</FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Enter Your Confirm Password"
                        {...field}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            <div className="mt-8 text-center">
              <Button type="submit" className="h-auto px-20 py-3 rounded-full cursor-pointer">
                Update
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default PasswordChangeForm;
