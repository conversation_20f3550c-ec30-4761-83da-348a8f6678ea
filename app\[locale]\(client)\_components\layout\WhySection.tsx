import React from "react";
import Link from "next/link";
import Image from "next/image";
import { DynamicIcon, dynamicIconImports } from "lucide-react/dynamic";
import { Button } from "@/components/ui/button";
import { getTranslations } from "next-intl/server";

const WhySection = async () => {
  const t = await getTranslations("HomePage.WhySection");
  const title = t("title");
  const description = t("description");
  const image = t("image");
  const points = t.raw("points");
  const buttonText = t("buttonText");
  const buttonLink = t("buttonLink");

  return (
    <section className="py-[60px]">
      <div className="container">
        <div className="flex flex-auto flex-col md:flex-row items-center gap-8 xl:gap-[95px]">
          <div className="w-full md:flex-1">
            <Image src={image} alt={title} width={800} height={800} priority className="w-full" />
          </div>
          <div className="flex-1 space-y-5">
            <h2 className="text-3xl xl:text-[50px] font-bold text-secondary">{title}</h2>
            <p className="text-gray-600 text-base lg:text-[22px] leading-relaxed">{description}</p>
            <ul className="grid grid-cols-1 lg:grid-cols-2 gap-4">
              {points.map(
                (point: { icon: keyof typeof dynamicIconImports; text: string }, idx: number) => (
                  <li key={idx} className="flex items-center gap-[15px]">
                    <span className="w-5 h-5 p-1 bg-[#f9f9f9] text-secondary inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name={point.icon} size={30} />
                    </span>
                    <span className="text-secondary text-base">{point.text}</span>
                  </li>
                ),
              )}
            </ul>
            <Button asChild className="px-8! py-3 w-auto h-auto rounded-full">
              <Link href={buttonLink}>
                {buttonText} <DynamicIcon name="arrow-right" size={20} />
              </Link>
            </Button>
          </div>
        </div>
      </div>
    </section>
  );
};

export default WhySection;
