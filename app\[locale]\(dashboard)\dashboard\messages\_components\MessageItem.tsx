import React from "react";
import { userIntentLabel } from "@/constants/listing.constants";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";

interface Args {
  member: any;
}

const MessageItem = ({ member }: Args) => {
  const unseenMessages = Array.from({ length: 3 }, (_, i) => i + 1);
  const profileUrl = member?.avatar?.filePath
    ? process.env.NEXT_PUBLIC_CDN_URL +
      "/" +
      member?.avatar?.filePath +
      "/" +
      member?.avatar?.fileName
    : "";

  return (
    <div className="flex items-center gap-3">
      <div className="relative">
        <Avatar className="w-12 h-12">
          <AvatarImage
            src={
              profileUrl ||
              "http://cdn.gayroom8.com//user/profile/houcinencibB4TjXnI0Y2cunsplashjpg1752749173997ctoke.jpg"
            }
            alt={`${member.firstName} ${member.lastName}`}
            width={60}
            height={60}
            className="object-cover"
          />
          <AvatarFallback>{member.firstName}</AvatarFallback>
        </Avatar>
        <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-600 border-2 border-white rounded-full" />
      </div>
      <div className="flex-1">
        <h4 className="text-base lg:text-lg font-bold text-gray-700">
          {member.firstName + " " + member.lastName}
        </h4>
        <div className="flex justify-between items-center gap-2">
          <p className="text-sm text-gray-500">{userIntentLabel[member.intent]}</p>
          <span className="w-5 h-5 flex items-center justify-center text-xs text-white rounded-full bg-primary">
            {unseenMessages.length}
          </span>
        </div>
      </div>
    </div>
  );
};

export default MessageItem;
