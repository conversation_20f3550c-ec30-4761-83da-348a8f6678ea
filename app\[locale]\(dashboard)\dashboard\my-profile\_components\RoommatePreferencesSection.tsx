"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import RoommatePreferencesForm from "./RoommatePreferencesForm";
import { UserProfile } from "@/typescript/interfaces";
import {
  ageRangesLabel,
  preferredGenderIdentityLabels,
  sexualOrientationsLabel,
  smokingHabitsLabel,
} from "@/constants/listing.constants";

interface Args {
  user: UserProfile;
}

const RoommatePreferencesSection = ({ user }: Args) => {
  const [isEdit, setIsEdit] = useState<boolean>(false);
  return isEdit ? (
    <RoommatePreferencesForm isEdit={isEdit} setIsEdit={setIsEdit} user={user} />
  ) : (
    <div className="space-y-7 bg-white shadow p-[20px] rounded-md border">
      <div className="space-y-[30px] relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="user-round-search" size={24} /> Roommate Preferences
        </h2>
      </div>
      <div className="space-y-5">
        <div className="space-y-3">
          <ul className="text-gray-600 grid grid-cols-1 gap-4">
            <li className="flex items-center gap-[10px]">
              <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                <DynamicIcon name="check" size={15} />
              </span>{" "}
              Gender Identity:{" "}
              {user.preferredGenderIdentity
                ?.map((item) => preferredGenderIdentityLabels[item])
                .join(", ") || "N/A"}
            </li>
            <li className="flex items-center gap-[10px]">
              <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                <DynamicIcon name="check" size={15} />
              </span>{" "}
              Sexual Orientation:{" "}
              {user.preferredSexualOrientation
                ?.map((item) => sexualOrientationsLabel[item])
                .join(", ") || "N/A"}
            </li>
            <li className="flex items-center gap-[10px]">
              <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                <DynamicIcon name="check" size={15} />
              </span>{" "}
              Age Range: 
              {user.preferredAgeRange?.map((item) => ageRangesLabel[item]).join(", ") || "N/A"}
            </li>
            <li className="flex items-center gap-[10px]">
              <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                <DynamicIcon name="check" size={15} />
              </span>{" "}
              Smoking Habits: 
              {user.preferredSmokingHabits?.map((item) => smokingHabitsLabel[item]).join(", ") ||
                "N/A"}
            </li>
          </ul>
        </div>
      </div>
    </div>
  );
};

export default RoommatePreferencesSection;
