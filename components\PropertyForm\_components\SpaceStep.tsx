"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  allowPets,
  amenities,
  bathRooms,
  bedRooms,
  brightness,
  neighthborhoods,
  parkings,
  residenceTypes,
  smokeCigarettes,
  smokeMarijuana,
} from "@/constants/constants";
import { Textarea } from "@/components/ui/textarea";
import { SpaceData, spaceSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { MultiSelect } from "@/components/ui/MultiSelect";
// import { GeoLocation } from "@/components/GeoLocation/GeoLocation";
import { TFormMode } from "@/typescript/types";
import { PropertyData } from "@/typescript/interfaces";
import GoogleMapsProvider from "@/components/GoogleMapsProvider";
import LocationAutocomplete from "@/components/LocationAutocomplete/LocationAutocomplete";
import { formatNumberWithCommas, isValidNumberInput, removeCommas } from "@/utils/numberFormatter";

interface Args {
  mode: TFormMode;
  listing?: PropertyData;
}

const SpaceStep = ({ mode, listing }: Args) => {
  // console.log("Listing Space: ", listing);
  const router = useRouter();
  const { currentStep, steps, setPropertyData, data, setCurrentStep, markStepCompleted } =
    useSignupStore();
  const form = useForm<SpaceData>({
    resolver: zodResolver(spaceSchema),
    defaultValues: {
      fullAddress: listing?.fullAddress || data.property.fullAddress || "",
      residenceType: listing?.residenceType || data.property.residenceType || "",
      size: listing?.size || data.property.size || "",
      bedrooms: listing?.bedrooms || data.property.bedrooms || "",
      bathrooms: listing?.bathrooms || data.property.bathrooms || "",
      brightness: data.property.brightness || "",
      amenities: listing?.amenities || data.property.amenities || [],
      parking: listing?.parking || data.property.parking || [],
      neighborhood: listing?.neighborhood || data.property.neighborhood || [],
      allowedPets: listing?.allowedPets || data.property.allowedPets || [],
      smokeCigarettes: listing?.smokeCigarettes || data.property?.smokeCigarettes?.toString() || "",
      smokeMarijuana: listing?.smokeMarijuana || data.property?.smokeMarijuana?.toString() || "",
      homeDescription: listing?.homeDescription || data.property.homeDescription || "",
      location:
        data.property.location && "coordinates" in data.property.location
          ? data.property.location.coordinates
          : data.property.location || [0, 0],
      locationLabel: listing?.locationLabel || data.property.locationLabel || "",
    },
  });

  const locationURL =
    mode === "add" ? "/dashboard/my-spaces/add" : `/dashboard/my-spaces/edit/${listing?._id}`;

  function onSubmit(values: any) {
    setPropertyData(values);
    markStepCompleted(currentStep);
    setCurrentStep(currentStep + 1);
    router.push(`${locationURL}/${currentStep + 1}`);
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`${locationURL}/${currentStep - 1}`);
  };

  const handleNext = () => {
    // Check schema is valid
    if (!form.formState.isValid) return;
    if (currentStep === steps.length - 1) return;
    markStepCompleted(currentStep);
    setCurrentStep(currentStep + 1);
    router.push(`${locationURL}/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="fullAddress"
          control={form.control}
          render={({}) => (
            <FormItem>
              <FormLabel className="input-label">
                Full Address<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <GoogleMapsProvider>
                  <LocationAutocomplete
                    className="input-field"
                    placeholder="Enter your location"
                    onSelect={(data) => {
                      form.setValue("fullAddress", data.address);
                      form.setValue("location", [data.latLng.lng, data.latLng.lat]);
                      form.setValue("locationLabel", data.address);
                    }}
                  />
                </GoogleMapsProvider>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="residenceType"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Residence Type<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select a residence type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {residenceTypes.map((residence, idx) => (
                    <SelectItem key={idx} value={residence.value}>
                      {residence.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="size"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Size (in square feet)<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Size"
                  {...field}
                  value={formatNumberWithCommas(field.value || "")}
                  onChange={(e) => {
                    const raw = removeCommas(e.target.value);
                    if (isValidNumberInput(raw)) {
                      field.onChange(raw);
                    }
                  }}
                  className="input-field"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bedrooms"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Bedrooms<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select a residence type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bedRooms.map((bedRoom, idx) => (
                    <SelectItem key={idx} value={bedRoom.value}>
                      {bedRoom.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bathrooms"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Bathrooms<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select a residence type" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bathRooms.map((bathRoom, idx) => (
                    <SelectItem key={idx} value={bathRoom.value}>
                      {bathRoom.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="brightness"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Brightness<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {brightness.map((brightness, idx) => (
                    <SelectItem key={idx} value={brightness.value}>
                      {brightness.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="amenities"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Amenities<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={amenities}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  value={field.value}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="parking"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Parking<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={parkings}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  value={field.value}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="neighborhood"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Neighborhood<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={neighthborhoods}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  value={field.value}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="allowedPets"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Allowed Pets<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={allowPets}
                  onValueChange={field.onChange}
                  defaultValue={field.value}
                  value={field.value}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeCigarettes"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Smoke Cigarettes<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select smoke cigarettes" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeCigarettes.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeMarijuana"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Smoke Marijuana<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select smoke marijuana" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeMarijuana.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="homeDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Home Description<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Home Description" {...field} className="h-40" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button
            type="submit"
            onClick={handleNext}
            disabled={currentStep === steps.length - 1}
            className="flex-1 !py-6 cursor-pointer"
          >
            {currentStep === steps.length - 1 ? "Complete" : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default SpaceStep;
