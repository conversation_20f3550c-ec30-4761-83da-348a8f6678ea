import { dynamicIconImports } from "lucide-react/dynamic";
export type TFeaturedCard = {
  icon: keyof typeof dynamicIconImports;
  title: string;
  description: string;
  image: string;
  reverse?: boolean;
};

export type THeroContent = {
  title: string;
  description: string;
  image: string;
};

export type TermsOfUse = {
  title: string;
  contents: string[];
};

export type PrivacyPolicy = {
  title: string;
  contents: string[];
};

export type FAQ = {
  _id: string;
  title: string;
  description: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type ExploreSpace = {
  id: number | string;
  images: string[];
  spaceType: string;
  houseType: string;
  location: string;
  aminities: string[];
  price: {
    rent: number;
    unit: string;
  };
  availableFrom: string;
};

export type Plan = {
  _id: string;
  name: string;
  price: number;
  duration: number;
  isActive: boolean;
  features?: string[];
  createdAt: string;
  updatedAt: string;
};

export type Blog = {
  _id: string;
  title: string;
  slug: string;
  summary: string;
  thumbnail: string;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
};

export type ImageType = {
  originalName: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  filePath: string;
};

export type TFormMode = "add" | "edit";

export type Pagination = {
  page: number;
  limit: number;
  total: number;
};

export type SocialAuthData = {
  type: string;
  code: string;
  provider: string;
};
