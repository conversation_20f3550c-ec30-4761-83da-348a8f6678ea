"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { handleManageSubscription } from "@/actions/subscriber.action";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { useCookies } from "react-cookie";

const MembershipStatusAction = () => {
  const [cookies] = useCookies(["token"]);
  const token = cookies.token;
  const router = useRouter();

  const handleManage = async () => {
    const res = await handleManageSubscription(token);
    if (res?.status >= 200 && res?.status < 300) {
      toast.success(res.message);
      router.push(res.url);
    } else {
      toast.error(res?.message);
    }
  };

  return (
    <div className="space-x-4">
      <Button className="rounded-full cursor-pointer" onClick={handleManage}>
        Manage
      </Button>
      <Button variant={"destructive"} className="rounded-full cursor-pointer">
        Cancel
      </Button>
    </div>
  );
};

export default MembershipStatusAction;
