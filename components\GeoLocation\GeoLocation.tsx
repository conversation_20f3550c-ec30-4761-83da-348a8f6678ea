"use client";

import React, { useState } from "react";
// import { useLoadScript } from "@react-google-maps/api";
import usePlacesAutocomplete, { getGeocode } from "use-places-autocomplete";
import { Check, ChevronsUpDown } from "lucide-react";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";

interface GeoLocationProps {
  form?: any;
  formValue?: any;
  onSelect?: (data: {
    address: string;
    latLng: { lat: number; lng: number };
    geocodeResult: google.maps.GeocoderResult;
  }) => void;
}

// const libraries: ("places" | "geometry" | "drawing")[] = ["places"];

export function GeoLocation({ form, formValue = "", onSelect }: GeoLocationProps) {
  // const { isLoaded } = useLoadScript({
  //   googleMapsApiKey: "AIzaSyDszBcrWGJLdWAUSPSCvyumnGczBZELFbY",
  //   libraries: libraries,
  // });

  // if (!isLoaded) return <div>Loading Google Maps...</div>;

  return <LocationCombobox form={form} formValue={formValue} onSelect={onSelect} />;
}

const LocationCombobox = ({ form, formValue, onSelect }: GeoLocationProps) => {
  const {
    ready,
    value,
    setValue,
    suggestions: { status, data },
    clearSuggestions,
  } = usePlacesAutocomplete();

  const [open, setOpen] = useState(false);
  const [selected, setSelected] = useState("");

  if (!ready) return <div>Loading Places Autocomplete...</div>;

  const handleSelect = async (address: string) => {
    setSelected(address);
    setValue(address);
    clearSuggestions();
    setOpen(false);

    const results = await getGeocode({ address });
    const result = results[0];
    const latLng = {
      lat: result.geometry.location.lat(),
      lng: result.geometry.location.lng(),
    };

    if (form && formValue) {
      form.setValue(formValue, address, { shouldDirty: true, shouldValidate: true });
    }

    if (onSelect) {
      onSelect({
        address,
        latLng,
        geocodeResult: result,
      });
    }
  };

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between px-4 py-6"
        >
          {selected || "Enter Your Location"}
          <ChevronsUpDown className="ml-2 h-4 w-4 opacity-50" />
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-full p-0">
        <Command>
          <CommandInput
            placeholder="Enter Your Location"
            value={value}
            onValueChange={setValue}
            className="h-9"
          />
          <CommandList>
            {status === "OK" ? (
              <CommandGroup>
                {data.map((item) => (
                  <CommandItem key={item.place_id} value={item.description} onSelect={handleSelect}>
                    {item.description}
                    <Check
                      className={cn(
                        "ml-auto",
                        selected === item.description ? "opacity-100" : "opacity-0",
                      )}
                    />
                  </CommandItem>
                ))}
              </CommandGroup>
            ) : (
              <CommandEmpty>No results found.</CommandEmpty>
            )}
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  );
};
