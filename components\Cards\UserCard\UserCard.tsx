import React from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { UserProfile } from "@/typescript/interfaces";
import { DynamicIcon } from "lucide-react/dynamic";
import { Badge } from "@/components/ui/badge";
import { preferredGenderIdentityLabels, userIntentLabel } from "@/constants/listing.constants";

interface UserCardProps {
  user: UserProfile;
}

const UserCard = ({ user }: UserCardProps) => {
  const fullName = user.firstName + " " + user.lastName;
  const profileUrl = user?.avatar?.filePath
    ? process.env.NEXT_PUBLIC_CDN_URL + "/" + user?.avatar?.filePath + "/" + user?.avatar?.fileName
    : "";

  return (
    <Card className="py-[10px] gap-[5px]">
      <CardContent className="text-center space-y-2 px-[10px]">
        <div className="flex justify-between items-center mb-4">
          <Badge className="px-3 py-2 bg-primary/70 text-[11px] rounded-[10px]">
            {user.isIdVerified ? "ID Verified" : "ID Not Verified"}
          </Badge>
          <Badge
            className={`px-3 py-2 ${user?.isAccountVerified ? "bg-secondary/70" : "bg-[#afafaf]"} text-[11px] rounded-[10px]`}
          >
            {user?.isAccountVerified ? "Account Verified" : "Account Not Verified"}
          </Badge>
        </div>
        <Link href={`/explore-faces/${user._id}`} className="space-y-2">
          <div className="relative w-40 h-40 mx-auto">
            <Image
              src={profileUrl}
              alt={fullName}
              width={150}
              height={150}
              priority
              className="rounded-full mx-auto outline-2 outline-secondary outline-offset-8 w-[150px] h-[150px] object-cover"
            />
            {user.isAccountVerified && user.isIdVerified && (
              <span className="absolute bottom-0 right-5 w-6 h-6 p-1 bg-secondary text-white inline-flex items-center justify-center rounded-full">
                <DynamicIcon name="check" size={20} />
              </span>
            )}
          </div>
          <CardTitle className="text-[27px] font-medium text-[#555555]">{fullName}</CardTitle>
          <p className="text-[16px] text-secondary">
            {userIntentLabel[user.intent ?? ""] || "N/A"}
          </p>
        </Link>
      </CardContent>
      <CardFooter className="border-t-2 border-dashed px-[30px]">
        <div className="flex justify-between w-full mt-[8px]">
          <p className="flex items-center gap-2 text-sm 2xl:text-[15px] text-[#555555]">
            <span className="w-9 h-9 p-2 bg-primary text-white inline-flex items-center justify-center rounded-full">
              <DynamicIcon name="venus-and-mars" size={26} />
            </span>
            {preferredGenderIdentityLabels[user.genderIdentity ?? ""] || "N/A"}
          </p>
          <p className="flex items-center gap-2 text-sm 2xl:text-[15px] text-[#555555]">
            <span className="w-9 h-9 p-2 bg-primary text-white inline-flex items-center justify-center rounded-full">
              <DynamicIcon name="id-card" size={25} />
            </span>
            Age: {user.age || "N/A"}
          </p>
        </div>
      </CardFooter>
    </Card>
  );
};

export default UserCard;
