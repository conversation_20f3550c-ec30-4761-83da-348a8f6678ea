"use client";

import { FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import React from "react";

import { UseFormReturn } from "react-hook-form";

interface IntendantFormValues {
  firstName: string;
  lastName: string;
  email: string;
  intent: string;
  spaceType: string;
}

const IntendantStep = ({ form }: { form: UseFormReturn<IntendantFormValues> }) => {
  return (
    <>
      <FormField
        name="intent"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>I want to</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select an option" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="rent">Rent</SelectItem>
                <SelectItem value="find">Find</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
      <FormField
        name="spaceType"
        control={form.control}
        render={({ field }) => (
          <FormItem>
            <FormLabel>Space Type</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select a space type" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="entire-place">Entire Place</SelectItem>
                <SelectItem value="private-room">Private Room</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )}
      />
    </>
  );
};

export default IntendantStep;
