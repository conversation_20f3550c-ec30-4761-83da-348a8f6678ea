"use client";

import * as React from "react";
import * as SliderPrimitive from "@radix-ui/react-slider";

import { cn } from "@/lib/utils";
import { Badge } from "./badge";

function Slider({
  className,
  defaultValue,
  value,
  min = 0,
  max = 100,
  ...props
}: React.ComponentProps<typeof SliderPrimitive.Root>) {
  const _values = React.useMemo(
    () => (Array.isArray(value) ? value : Array.isArray(defaultValue) ? defaultValue : [min, max]),
    [value, defaultValue, min, max],
  );

  return (
    <div className="relative w-full flex flex-col items-center">
      <SliderPrimitive.Root
        data-slot="slider"
        defaultValue={defaultValue}
        value={value}
        min={min}
        max={max}
        className={cn(
          "relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",
          className,
        )}
        {...props}
      >
        <SliderPrimitive.Track
          data-slot="slider-track"
          className={cn(
            "bg-muted relative grow rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5",
          )}
        >
          <SliderPrimitive.Range
            data-slot="slider-range"
            className={cn(
              "bg-primary absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full opacity-100",
            )}
          />
        </SliderPrimitive.Track>
        {Array.from({ length: _values.length }, (_, index) => (
          <SliderPrimitive.Thumb
            data-slot="slider-thumb"
            key={index}
            className="group border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"
          >
            <Badge className="absolute hidden group-hover:flex left-1/2 -translate-x-1/2 -translate-y-1/2 -top-5">
              <span>{_values[index]}</span>
              <div className="absolute border-[6px] left-1/2 -translate-x-1/2 border-transparent border-t-primary top-full" />
            </Badge>
          </SliderPrimitive.Thumb>
        ))}
      </SliderPrimitive.Root>
    </div>
  );
}

export { Slider };

// "use client";

// import * as React from "react";
// import * as SliderPrimitive from "@radix-ui/react-slider";

// import { cn } from "@/lib/utils";
// import { Badge } from "./badge";

// function Slider({
//   className,
//   defaultValue,
//   value,
//   min = 0,
//   max = 100,
//   ...props
// }: React.ComponentProps<typeof SliderPrimitive.Root>) {
//   const _values = React.useMemo(
//     () => (Array.isArray(value) ? value : Array.isArray(defaultValue) ? defaultValue : [min, max]),
//     [value, defaultValue, min, max],
//   );

//   return (
//     <div className="relative w-full flex flex-col items-center">
//       <SliderPrimitive.Root
//         data-slot="slider"
//         defaultValue={defaultValue}
//         value={value}
//         min={min}
//         max={max}
//         className={cn(
//           "relative flex w-full touch-none items-center select-none data-[disabled]:opacity-50 data-[orientation=vertical]:h-full data-[orientation=vertical]:min-h-44 data-[orientation=vertical]:w-auto data-[orientation=vertical]:flex-col",
//           className,
//         )}
//         {...props}
//       >
//         <SliderPrimitive.Track
//           data-slot="slider-track"
//           className={cn(
//             "bg-muted relative grow overflow-hidden rounded-full data-[orientation=horizontal]:h-1.5 data-[orientation=horizontal]:w-full data-[orientation=vertical]:h-full data-[orientation=vertical]:w-1.5",
//           )}
//         >
//           <SliderPrimitive.Range
//             data-slot="slider-range"
//             className={cn(
//               "bg-primary! absolute data-[orientation=horizontal]:h-full data-[orientation=vertical]:w-full",
//             )}
//           />
//         </SliderPrimitive.Track>
//         {Array.from({ length: _values.length }, (_, index) => (
//           <SliderPrimitive.Thumb
//             data-slot="slider-thumb"
//             key={index}
//             className="group border-primary bg-background ring-ring/50 block size-4 shrink-0 rounded-full border shadow-sm transition-[color,box-shadow] hover:ring-4 focus-visible:ring-4 focus-visible:outline-hidden disabled:pointer-events-none disabled:opacity-50"
//           >
//             <Badge className="absolute hidden group-hover:flex left-1/2 -translate-x-1/2 -translate-y-1/2 -top-5">
//               <span>{_values[index]}</span>
//               {/* Arrow */}
//               <div className="absolute border-[6px] left-1/2 -translate-x-1/2 border-transparent border-t-primary top-full" />
//             </Badge>
//           </SliderPrimitive.Thumb>
//         ))}
//       </SliderPrimitive.Root>
//     </div>
//   );
// }

// export { Slider };
