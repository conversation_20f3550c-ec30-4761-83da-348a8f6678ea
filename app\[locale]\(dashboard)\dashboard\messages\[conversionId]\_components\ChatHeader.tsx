import Image from "next/image";

const ChatHeader = ({ name, status, avatar }: any) => (
  <div className="flex items-center p-4 border-b border-primary">
    <Image
      src={avatar}
      alt={`${name} Avatar`}
      width={40}
      height={40}
      className="w-10 h-10 rounded-full mr-3"
    />
    <div>
      <h2 className="font-bold text-secondary text-lg">{name}</h2>
      <p className="text-sm text-gray-500">{status}</p>
    </div>
  </div>
);

export default ChatHeader;
