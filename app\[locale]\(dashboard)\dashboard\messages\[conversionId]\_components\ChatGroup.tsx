import Image from "next/image";
import { Message } from "@/typescript/interfaces";

interface MessageGroupProps {
  sender: {
    name: string;
    avatar: string;
    isCurrentUser: boolean;
  };
  chat: Message;
}

const MessageGroup = ({ sender, chat }: MessageGroupProps) => (
  <div className={`flex flex-col ${sender.isCurrentUser ? "items-end" : "items-start"} my-4`}>
    {!sender.isCurrentUser && (
      <div className="flex items-center gap-2 mb-1">
        <Image
          src={sender.avatar}
          alt={`${sender.name} Avatar`}
          width={30}
          height={30}
          className="w-8 h-8 rounded-full"
        />
        <span className="font-semibold">{sender.name}</span>
      </div>
    )}
    <MessageBubble text={chat.message} isCurrentUser={sender.isCurrentUser} />
  </div>
);

const MessageBubble = ({ text, isCurrentUser }: { text: string; isCurrentUser: boolean }) => (
  <div
    className={`relative bg-gray-100 px-4 py-2 rounded-lg max-w-xs my-1 ${
      isCurrentUser ? "bg-blue-100 ml-auto" : "bg-gray-100"
    }`}
  >
    {text}
    <span className="absolute top-1 right-1 text-gray-400 text-sm">⋮</span>
  </div>
);

export default MessageGroup;
