"use client";

import React from "react";
import Link from "next/link";
import Image from "next/image";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Footer, <PERSON>Title } from "@/components/ui/card";
import { UserProfile } from "@/typescript/interfaces";
import { DynamicIcon } from "lucide-react/dynamic";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { preferredGenderIdentityLabels, userIntentLabel } from "@/constants/listing.constants";
import { authStore, AuthStore } from "@/store/authStore";
import { createConversation } from "@/actions/message.action";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";
import { useCookies } from "react-cookie";

interface ProfileCardProps {
  user: UserProfile;
}

const ProfileCard = ({ user }: ProfileCardProps) => {
  const router = useRouter();
  const { isAuthenticated, user: currentUser } = authStore((state: AuthStore) => state);
  const fullName = user.firstName + " " + user.lastName;
  const profileUrl = user?.avatar?.filePath
    ? process.env.NEXT_PUBLIC_CDN_URL + "/" + user?.avatar?.filePath + "/" + user?.avatar?.fileName
    : "";
  const [cookies] = useCookies(["token"]);

  // console.log(cookies);

  const isSelfProfile = user?._id === currentUser?._id;
  const canSendMessage = currentUser?.membership?.status === "active" || false;

  const handleCreateConversation = async () => {
    if (!isAuthenticated) {
      toast.error("You need to be logged in to send messages.");
      return;
    }
    if (!canSendMessage) {
      toast.error("You need to be subscribed to send messages.");
      router.push("/dashboard/membership");
      return;
    }
    const response = await createConversation(user?._id as string, cookies.token);

    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message || "Conversation created successfully!");
      router.push(`/dashboard/messages/${response.conversationId}`);
    } else {
      toast.error(response.message || "Failed to create conversation. Please try again.");
      router.push("/dashboard/messages");
    }
  };

  return (
    <Card className="gap-[9px]">
      <CardContent className="text-center space-y-2">
        <div className="flex justify-end items-center mb-4">
          <Badge className="text-[10px] bg-secondary rounded-full px-[14px] py-[5px]">
            {user.isAccountVerified ? "Account Verified" : "Account Not Verified"}
          </Badge>
        </div>
        <div className="relative w-40 h-40 mx-auto">
          <Image
            src={profileUrl}
            alt={fullName}
            width={150}
            height={150}
            priority
            className="rounded-full mx-auto outline-2 outline-secondary outline-offset-8 w-[150px] h-[150px] object-cover"
          />
          {user.isAccountVerified && user.isIdVerified && (
            <span className="absolute bottom-0 right-5 w-6 h-6 p-1 bg-secondary text-white inline-flex items-center justify-center rounded-full">
              <DynamicIcon name="check" size={20} />
            </span>
          )}
        </div>
        <Link href={`/explore-faces/${user._id}`}>
          <CardTitle className="text-2xl text-[#555555] text-[27px] font-medium">
            {fullName}
          </CardTitle>
        </Link>
        <p className="text-lg text-secondary">{userIntentLabel[user.intent ?? ""] || "N/A"}</p>
      </CardContent>
      <CardFooter className="border-t-2 border-dashed flex-col gap-5">
        <div className="flex justify-between w-full mt-[11px] px-[10px]">
          <p className="flex items-center gap-2 text-sm 2xl:text-[15px] text-[#555555]">
            <span className="w-9 h-9 p-2 bg-secondary text-white inline-flex items-center justify-center rounded-full">
              <DynamicIcon name="venus-and-mars" size={25} />
            </span>
            {preferredGenderIdentityLabels[user.genderIdentity ?? ""] || "N/A"}
          </p>
          <p className="flex items-center gap-2 text-sm 2xl:text-[15px] text-[#555555]">
            <span className="w-9 h-9 p-2 bg-secondary text-white inline-flex items-center justify-center rounded-full">
              <DynamicIcon name="id-card" size={25} />
            </span>
            Age: {user.age || "N/A"}
          </p>
        </div>
        {!isSelfProfile && (
          <Button
            className="w-full h-auto rounded-full text-[15px] font-medium"
            onClick={handleCreateConversation}
          >
            <DynamicIcon name="user-round-check" size={24} />{" "}
            {canSendMessage ? "Send Message" : "Subscribe to Send Message"}
          </Button>
        )}

        {isSelfProfile && (
          <p className="text-sm text-muted-foreground text-center w-full">
            This is your own profile. You can&apos;t message yourself.
          </p>
        )}
      </CardFooter>
    </Card>
  );
};

export default ProfileCard;

/**
 * href={
                isAuthenticated
                  ? canSendMessage
                    ? `/dashboard/messages/${user._id}`
                    : `/dashboard/membership`
                  : "/auth/register"
              }
 */
