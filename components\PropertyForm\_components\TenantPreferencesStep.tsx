"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Button } from "@/components/ui/button";
import { TenantPrefsData, tenantPrefsSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
// import { handleSignup } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { TFormMode } from "@/typescript/types";
import { PropertyData } from "@/typescript/interfaces";

interface Args {
  mode: TFormMode;
  listing?: PropertyData;
}

const createListing = async (data: any) => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;
  const token = document.cookie
    .split("; ")
    .find((row) => row.startsWith("token="))
    ?.split("=")[1];

  try {
    const response = await fetch(`${baseURL}/web/space`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const resData = await response.json();
      return {
        message: resData.message,
        status: response.status,
      };
    } else {
      return {
        message: "An error occurred",
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

const TenantPreferencesStep = ({ mode, listing }: Args) => {
  const router = useRouter();
  const {
    currentStep,
    steps,
    setPropertyData,
    setCurrentStep,
    data,
    clear,
    markStepCompleted,
    resetCompletedSteps,
  } = useSignupStore();
  const form = useForm<TenantPrefsData>({
    resolver: zodResolver(tenantPrefsSchema),
    defaultValues: {
      idealTenantDescription:
        listing?.idealTenantDescription || data.property.idealTenantDescription || "",
    },
  });

  const locationURL =
    mode === "add" ? "/dashboard/my-spaces/add" : `/dashboard/my-spaces/edit/${listing?._id}`;

  async function onSubmit(values: TenantPrefsData) {
    setPropertyData(values);

    const isLastStep = currentStep === steps.length - 1;
    // const fullData = {
    //   property: {
    //     ...data.property,
    //     ...values,
    //     spaceType: data.user.spaceType,
    //   },
    // };

    // console.log("Property Data: ", fullData);

    if (isLastStep) {
      const fullData = {
        property: {
          ...data.property,
          ...values,
          spaceType: data.user.spaceType,
        },
      };

      const res = await createListing(fullData.property);
      if (res.status >= 200 && res.status < 300) {
        toast.success(res.message);
        markStepCompleted(currentStep);
        setCurrentStep(0);
        resetCompletedSteps();
        clear();
        router.push("/");
      } else {
        toast.error(res.message);
        console.log(res);
      }
    } else {
      markStepCompleted(currentStep);
      setCurrentStep(0);
      resetCompletedSteps();
      router.push(`${locationURL}/${currentStep + 1}`);
    }
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`${locationURL}/${currentStep - 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          name="idealTenantDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Describe Your Ideal Roommate<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Ideal roommate description" {...field} className="h-40" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Complete" : "Next"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default TenantPreferencesStep;
