const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const getAllMemberships = async () => {
  try {
    const response = await fetch(`${baseURL}/web/memberships`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });
    return response.json();
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};
