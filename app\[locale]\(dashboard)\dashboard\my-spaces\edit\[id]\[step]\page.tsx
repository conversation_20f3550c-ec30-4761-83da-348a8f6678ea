import React from "react";
import PropertyForm from "@/components/PropertyForm/PropertyForm";
import isAuthenticated from "@/utils/isAuthenticated";
import { getListingById } from "@/actions/spaces.action";

interface Args {
  params: Promise<{
    id: string;
  }>;
}

const EditMySpacePage = async ({ params }: Args) => {
  const user = (await isAuthenticated({ noRedirect: true }))?.user;
  const { id } = await params;
  const listing = (await getListingById(id))?.space;
  return <PropertyForm mode="edit" user={user} listing={listing} />;
};

export default EditMySpacePage;
