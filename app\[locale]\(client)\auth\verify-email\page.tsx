import React from "react";
import Link from "next/link";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import { redirect } from "next/navigation";
import { <PERSON>, CardContent, <PERSON>Footer, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import VerifyActiveButton from "./_components/VerifyActiveButton";

interface VerifyEmailPageProps {
  searchParams: Promise<{ token: string }>;
}

const VerifyEmailPage = async ({ searchParams }: VerifyEmailPageProps) => {
  const { token } = await searchParams;

  if (!token) {
    redirect("/");
  }

  return (
    <section className="space-y-4 pt-0">
      <TopBanner title="Email Verification" />
      <div className="container">
        <div className="space-y-5">
          <Card className="w-full max-w-lg mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl text-secondary font-semibold">
                Verify Your Email
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <p className="text-gray-700 text-base leading-relaxed">
                We have sent you an email to verify your email address. Please click on the link in
                the email to verify your email address.
              </p>
              <p>
                If you did not receive the email, please check your spam folder or{" "}
                <Link href={"/contact-us"} className="text-secondary underline">
                  contact us
                </Link>
                .
              </p>
            </CardContent>
            <CardFooter>
              <VerifyActiveButton token={token} />
            </CardFooter>
          </Card>
        </div>
      </div>
    </section>
  );
};

export default VerifyEmailPage;
