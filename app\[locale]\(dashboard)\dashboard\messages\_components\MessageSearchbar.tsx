"use client";

import React from "react";
import { Input } from "@/components/ui/input";
import { Form, FormControl, FormField, FormItem } from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodR<PERSON>olver } from "@hookform/resolvers/zod";

const messageSearchbarSchema = z.object({
  search: z.string().min(1, "Search term is required"),
});

const MessageSearchbar = () => {
  const form = useForm<any>({
    resolver: zodResolver(messageSearchbarSchema),
    defaultValues: {
      search: "",
    },
  });

  const onSubmit = (data: any) => {
    console.log(data);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          name="search"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormControl>
                <Input {...field} placeholder="Search" />
              </FormControl>
            </FormItem>
          )}
        />
      </form>
    </Form>
  );
};

export default MessageSearchbar;
