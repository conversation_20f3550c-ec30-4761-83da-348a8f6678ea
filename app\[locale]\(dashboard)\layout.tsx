import React from "react";
import Header from "@/components/common/Header";
import Footer from "@/components/common/Footer";
import { NextIntlClientProvider } from "next-intl";

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout = ({ children }: DashboardLayoutProps) => {
  return (
    <NextIntlClientProvider>
      <Header />
      <main>{children}</main>
      <Footer />
    </NextIntlClientProvider>
  );
};

export default DashboardLayout;
