"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import {
  ageRanges,
  preferredGenders,
  preferredSexualOrientations,
  smokingHabits,
} from "@/constants/constants";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Button } from "@/components/ui/button";
import { RoommateFindPrefsData, roommateFindPrefsSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { MultiSelect } from "@/components/ui/MultiSelect";
// import { handleSignup } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { TFormMode } from "@/typescript/types";

interface Args {
  mode: TFormMode;
}

const createListing = async (data: any) => {
  const baseURL = process.env.NEXT_PUBLIC_API_URL;
  const token = document.cookie
    .split("; ")
    .find((row) => row.startsWith("token="))
    ?.split("=")[1];

  try {
    const response = await fetch(`${baseURL}/web/space`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const resData = await response.json();
      return {
        message: resData.message,
        status: response.status,
      };
    } else {
      return {
        message: "An error occurred",
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

const FindRoommatePreferencesStep = ({ mode }: Args) => {
  const router = useRouter();
  const { currentStep, steps, data, setCurrentStep, clear, setUserData } = useSignupStore();
  const form = useForm<RoommateFindPrefsData>({
    resolver: zodResolver(roommateFindPrefsSchema),
    defaultValues: {
      preferredGenderIdentity: data.user.preferredGenderIdentity || [],
      preferredSexualOrientation: data.user.preferredSexualOrientation || [],
      preferredAgeRange: data.user.preferredAgeRange || [],
      preferredSmokingHabits: data.user.preferredSmokingHabits || [],
    },
  });

  const locationURL = mode === "add" ? "/dashboard/my-spaces/add" : "/dashboard/my-spaces/edit";

  async function onSubmit(values: RoommateFindPrefsData) {
    setUserData(values);
    // console.log("Find Roommate Preferences form data:", data);
    const fullDataToSend = {
      ...data,
      property: {
        ...data.property,
        ...values,
        spaceType: data.user.spaceType,
      },
      user: {
        ...data.user,
        ...values,
      },
    };
    // console.log(fullDataToSend);
    // const isLastStep = currentStep === steps.length - 1;
    // if (isLastStep) {
    //   router.push("/");
    //   clear();
    // } else {
    //   router.push(`/auth/onboarding/${currentStep + 1}`);
    // }
    const res = await createListing(fullDataToSend);
    if (res.status >= 200 && res.status < 300) {
      toast.success(res.message);
      router.push("/");
      setCurrentStep(0);
      clear();
    } else {
      toast.error(res.message);
      router.push("/");
      setCurrentStep(0);
      clear();
    }
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`${locationURL}/${currentStep - 1}`);
  };

  const handleNext = () => {
    // Check schema is valid
    if (!form.formState.isValid) return;
    if (currentStep === steps.length - 1) return;
    setCurrentStep(currentStep + 1);
    router.push(`${locationURL}/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="preferredGenderIdentity"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Gender Identity<span className="text-red-500">*</span>
              </FormLabel>
              {/* Single Select */}
              <FormControl>
                <MultiSelect
                  options={preferredGenders}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredSexualOrientation"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Sexual Orientation<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={preferredSexualOrientations}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredAgeRange"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Age Range<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={ageRanges} onValueChange={field.onChange} className="pl-6!" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredSmokingHabits"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Smoking Habits<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={smokingHabits}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" onClick={handleNext} className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Complete" : "Next"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default FindRoommatePreferencesStep;
