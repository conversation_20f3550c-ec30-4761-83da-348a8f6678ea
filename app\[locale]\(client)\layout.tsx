import React from "react";
import Header from "@/components/common/Header";
import Footer from "@/components/common/Footer";
import { hasLocale, NextIntlClientProvider } from "next-intl";
import { routing } from "@/i18n/routing";
import { notFound } from "next/navigation";

type ClientLayoutProps = {
  children: React.ReactNode;
  params: Promise<{ locale: string }>;
};

const ClientLayout = async ({ children, params }: ClientLayoutProps) => {
  const { locale } = await params;
  if (!hasLocale(routing.locales, locale)) {
    notFound();
  }
  return (
    <NextIntlClientProvider>
      <Header />
      <main>{children}</main>
      <Footer />
    </NextIntlClientProvider>
  );
};

export default ClientLayout;
