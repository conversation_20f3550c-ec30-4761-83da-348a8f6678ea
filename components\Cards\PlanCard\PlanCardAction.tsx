"use client";

import React from "react";
import toast from "react-hot-toast";
import { Button } from "@/components/ui/button";
import { handleSubscribe } from "@/actions/subscriber.action";
import { useRouter } from "next/navigation";

const PlanCardAction = ({ plan, color }: any) => {
  const router = useRouter();
  const handleMembershipPlan = async () => {
    const res = await handleSubscribe(plan._id);
    if (res?.status >= 200 && res?.status < 300) {
      toast.success(res.message);
      router.push(res.subscription);
    } else {
      toast.error(res?.message);
    }
  };
  return (
    <Button
      className={`w-full font-medium cursor-pointer px-[14px] py-[12px] text-[14px] rounded-[12px] ${color.bg}`}
      onClick={handleMembershipPlan}
    >
      Subscribe
    </Button>
  );
};

export default PlanCardAction;
