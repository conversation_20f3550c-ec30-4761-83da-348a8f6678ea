"use client";

import React, { useState } from "react";
import { DynamicIcon } from "lucide-react/dynamic";
import {
  cleanlinessLabel,
  describeMyselfAsLabel,
  smokingHabitsLabel,
  tranvelsLabel,
  workFromHomeLabel,
  zodiacSignLabel,
} from "@/constants/listing.constants";
import { UserProfile } from "@/typescript/interfaces";
import { Button } from "@/components/ui/button";
import AboutForm from "./AboutForm";

interface Args {
  isPrivateRoom?: boolean;
  user: UserProfile;
  showPersonalDetails?: boolean;
  showRentEntirePlaceFields?: boolean;
}

const AboutSection = ({ showPersonalDetails, user, showRentEntirePlaceFields }: Args) => {
  const [isEdit, setIsEdit] = useState<boolean>(false);

  return isEdit ? (
    <AboutForm
      isEdit={isEdit}
      setIsEdit={setIsEdit}
      user={user}
      showRentEntirePlaceFields={showRentEntirePlaceFields}
    />
  ) : (
    <div className="space-y-7 bg-white shadow rounded-md border p-[20px]">
      <div className="space-y-[3] relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="user" size={24} /> About {user?.firstName || "N/A"}
        </h2>
        <p>{user?.selfDescription}</p>
      </div>
      {showPersonalDetails && (
        <>
          {/* Basic */}
          <div className="space-y-[30px]">
            <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
              Lifestyle
            </h2>
            <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Work From Home: {workFromHomeLabel[user?.workFromHome ?? ""] || "N/A"}
              </li>
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Smoke Cigarettes: {smokingHabitsLabel[user?.smokeCigarettes ?? ""] || "N/A"}
              </li>
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Travel: {tranvelsLabel[user?.travel ?? ""] || "N/A"}
              </li>
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Smoke Marijuana: {smokingHabitsLabel[user?.smokeMarijuana ?? ""] || "N/A"}
              </li>
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                Cleanliness: {cleanlinessLabel[user?.cleanliness ?? ""] || "N/A"}
              </li>
            </ul>
          </div>
          {/* Describe Myself As */}
          <div className="space-y-3">
            <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
              I Describe Myself as...
            </h2>
            <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
              {(user.describeMyselfAs ?? []).map((item, idx) => (
                <li key={idx} className="flex items-center gap-[10px] capitalize">
                  <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                    <DynamicIcon name="check" size={15} />
                  </span>{" "}
                  {describeMyselfAsLabel[item] || "N/A"}
                </li>
              ))}
            </ul>
          </div>
          {/* Fluent Languages */}
          {user.fluentLanguages && user?.fluentLanguages?.length > 0 && (
            <div className="space-y-3">
              <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                Languages
              </h2>
              <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
                {user.fluentLanguages.map((language, idx) => (
                  <li key={idx} className="flex items-center gap-[10px] capitalize">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    {language}
                  </li>
                ))}
              </ul>
            </div>
          )}
          {/* Zodiac */}
          <div className="space-y-3">
            <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
              Zodiac Sign
            </h2>
            <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
              <li className="flex items-center gap-[10px]">
                <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                  <DynamicIcon name="check" size={15} />
                </span>{" "}
                {zodiacSignLabel[user?.zodiac ?? ""] || "N/A"}
              </li>
            </ul>
          </div>
        </>
      )}
    </div>
  );
};

export default AboutSection;
