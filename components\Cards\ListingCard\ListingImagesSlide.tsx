"use client";

import Image from "next/image";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { ImageType } from "@/typescript/types";
import "swiper/css/navigation";

interface ListingImagesSlideProps {
  images: ImageType[];
}

const ListingImagesSlide = ({ images }: ListingImagesSlideProps) => {
  return (
    <Swiper className="w-full" navigation={true} modules={[Navigation]}>
      {images.map((image: ImageType, idx: number) => {
        const imageUrl =
          process.env.NEXT_PUBLIC_CDN_URL + "/" + image.filePath + "/" + image.fileName;
        return (
          <SwiperSlide key={idx}>
            <Image
              src={imageUrl}
              alt={`Image ${idx}`}
              width={800}
              height={800}
              priority
              className="w-full h-[306px] object-cover rounded-lg"
            />
          </SwiperSlide>
        );
      })}
    </Swiper>
  );
};

export default ListingImagesSlide;
