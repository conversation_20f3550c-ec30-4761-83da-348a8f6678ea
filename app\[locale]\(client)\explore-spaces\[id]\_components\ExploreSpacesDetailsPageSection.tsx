import React from "react";
import ProfileCard from "@/components/Cards/ProfileCard/ProfileCard";
import ListingImagesSlide from "./ListingImagesSlide";
import { PropertyData, UserProfile } from "@/typescript/interfaces";
import { DynamicIcon } from "lucide-react/dynamic";
import { Badge } from "@/components/ui/badge";
import {
  ageRangesLabel,
  allowedGuestsLabel,
  allowedPetsLabels,
  amenitiesLabel,
  bathRoomLabels,
  bedRoomsizeLabel,
  bedRoomsLabel,
  brightnessLabels,
  furnishedLabels,
  leaseRequiredLabels,
  neighborhoodLabels,
  parkingsLabels,
  preferredGenderIdentityLabels,
  requiredReferencesLabels,
  roomFeaturesLabels,
  sexualOrientationsLabel,
  SpaceTypeLabels,
  timeDurationLabels,
  utilitiesIncludedLabels,
} from "@/constants/listing.constants";
import PropertyLocationMap from "@/components/PropertyLocationMap/PropertyLocationMap";
import AddToWishlist from "@/components/Buttons/AddToWishlist/AddToWishlist";
import ShareButton from "@/components/Buttons/ShareButton/ShareButton";
import formattedCurrency from "@/lib/currencyFormatter";
import formatDate from "@/utils/formatDate";
import GoogleMapsProvider from "@/components/GoogleMapsProvider";
import { numberWithCommas } from "@/lib/numberWithCommas";

interface ExploreSpacesDetailsPageSectionProps {
  listing: PropertyData;
}

const ExploreSpacesDetailsPageSection = ({ listing }: ExploreSpacesDetailsPageSectionProps) => {
  const isPrivateRoom = listing.spaceType === "private_room";
  const url = `${process.env.NEXT_PUBLIC_FRONTEND_URL}/explore-spaces/${listing._id}`;

  const user = listing.user;

  const coordinates = (listing?.location as { coordinates: [number, number] })?.coordinates;
  const center = {
    lat: coordinates?.[1] as number,
    lng: coordinates?.[0] as number,
  };

  return (
    <section className="py-[40px]">
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 lg:col-span-8 2xl:col-span-9 space-y-[30px]">
            <div className="space-y-4">
              <div className="flex justify-between items-center">
                <h2 className="text-lg md:text-2xl xl:text-[30px] font-bold text-secondary flex items-center gap-1.5">
                  <DynamicIcon name="map-pin-house" size={32} /> {listing.fullAddress}
                </h2>
                <Badge className="px-4 py-2 bg-secondary text-white rounded-full capitalize text-sm lg:text-base font-medium">
                  {SpaceTypeLabels[listing?.spaceType]}
                </Badge>
              </div>
              <div className="flex justify-between items-center">
                <p className="text-[#555555] text-base">
                  Rent{" "}
                  <span className="text-2xl xl:text-[32px] font-bold text-primary">
                    {formattedCurrency.format(listing.monthlyRent)}
                  </span>
                  <span className="text-primary text-[11px]">/month</span>
                </p>
                <div className="flex items-center gap-2">
                  <ShareButton url={url} />
                  <AddToWishlist
                    listingId={listing._id as string}
                    className="rounded-[10px] p-[20px]"
                  />
                </div>
              </div>
            </div>
            <ListingImagesSlide listing={listing} />
            {/* Availability */}
            <div className="flex justify-between items-center flex-wrap gap-2.5">
              <div className="flex items-center gap-[10px]">
                <div className="w-[53px] h-[53px] p-2 bg-white text-primary inline-flex items-center justify-center rounded-full shadow">
                  <DynamicIcon name="calendar-days" size={24} />
                </div>
                <div>
                  <h3 className="text-base text-secondary font-medium tracking-wider">
                    Availability Date
                  </h3>
                  <p className="text-[#555555] text-[14px] font-medium">
                    {formatDate(listing.availabilityDate as string)}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-[10px]">
                <div className="w-[53px] h-[53px] p-2 bg-white text-primary inline-flex items-center justify-center rounded-full shadow">
                  <DynamicIcon name="clock" size={24} />
                </div>
                <div>
                  <h3 className="text-base text-secondary font-medium tracking-wider">
                    Availability Duration
                  </h3>
                  <p className="text-[#555555] text-[14px] font-medium">
                    {/* {listing.availabilityDuration} */}
                    {timeDurationLabels[listing.availabilityDuration] || "N/A"}
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-[10px]">
                <div className="w-[53px] h-[53px] p-2 bg-white text-primary inline-flex items-center justify-center rounded-full shadow">
                  <DynamicIcon name="clock-alert" size={24} />
                </div>
                <div>
                  <h3 className="text-base text-secondary font-medium tracking-wider">
                    Minimum Duration
                  </h3>
                  <p className="text-[#555555] text-[14px] font-medium">
                    {timeDurationLabels[listing.minimumDuration] || "N/A"}
                  </p>
                </div>
              </div>
            </div>
            {/* Details */}
            <div className="space-y-7 bg-white shadow p-5 rounded-[10px]">
              <div className="space-y-3">
                <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
                  <DynamicIcon name="house" size={24} /> The Space
                </h2>
                <p>{listing.homeDescription}</p>
              </div>
              {/* Basic */}
              <div className="space-y-3">
                <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                  Basics
                </h2>
                <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
                  <li className="flex items-center gap-[10px] capitalize">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Residence Type: {listing?.residenceType || "N/A"}
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Deposit Amount: {formattedCurrency.format(listing?.depositAmount) || "N/A"}
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    <span>
                      Size: {numberWithCommas(listing?.size) || "N/A"}
                      ft<sup>2</sup>
                    </span>
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Parking:{" "}
                    {listing?.parking.map((parking) => parkingsLabels[parking]).join(", ") || "N/A"}
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Bedrooms: {bedRoomsLabel[listing?.bedrooms] || "N/A"}
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Brightness: {brightnessLabels[listing?.brightness] || "N/A"}
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Bathrooms: {listing?.bathrooms || "N/A"}
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Utilities Included:{" "}
                    {utilitiesIncludedLabels[listing?.utilitiesIncluded] || "N/A"}
                  </li>
                </ul>
              </div>
              {/* Amenities */}
              <div className="space-y-3">
                <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                  Amenities
                </h2>
                <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
                  {listing?.amenities.map((amenity, idx) => {
                    const label = amenitiesLabel[amenity];
                    return (
                      <li key={idx} className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        {label}
                      </li>
                    );
                  })}
                </ul>
              </div>
              {/* Rules */}
              <div className="space-y-3">
                <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                  Rules
                </h2>
                <ul className="text-gray-600 grid grid-cols-1 gap-4">
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Smoking: {listing?.smokeCigarettes ? "Yes" : "No"}
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Allow Pets:{" "}
                    {listing?.allowedPets.map((pet) => allowedPetsLabels[pet]).join(", ") ||
                      "No Pets"}
                  </li>
                  <li className="flex items-center gap-[10px] capitalize">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Overnight Guests: {allowedGuestsLabel[listing?.overnightGuestsAllowed] || "N/A"}
                  </li>
                </ul>
              </div>
              {/* Rental Requirements */}
              <div className="space-y-3">
                <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                  Rental Requirements
                </h2>
                <ul className="text-gray-600 grid grid-cols-1 gap-4">
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Lease Required: {leaseRequiredLabels[listing?.leaseRequired] || "N/A"}
                  </li>
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Required References:{" "}
                    {requiredReferencesLabels[listing?.requiredReferences] || "N/A"}
                  </li>
                </ul>
              </div>
            </div>
            {/* Private room details */}
            {isPrivateRoom && (
              <div className="space-y-7 bg-white shadow p-5 rounded-md">
                <div className="space-y-7">
                  <h2 className="text-[22px] font-semibold text-secondary flex items-center gap-2.5">
                    <DynamicIcon name="house-plug" size={24} /> The Room
                  </h2>
                  <div className="space-y-3">
                    <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                      Basics
                    </h2>
                    <ul className="text-gray-600 grid grid-cols-1 gap-4">
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Furnished: {furnishedLabels[listing?.furnished] || "N/A"}
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Size: {bedRoomsizeLabel[listing?.bedroomSize] || "N/A"}
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Bathroom: {bathRoomLabels[listing?.bathroom] || "N/A"}
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Brightness: {brightnessLabels[listing?.brightness] || "N/A"}
                      </li>
                    </ul>
                  </div>
                  <div className="space-y-3">
                    <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                      Room Features
                    </h2>
                    <ul className="text-[#555555] grid grid-cols-1 gap-4">
                      {listing?.roomFeatures.map((feature, idx) => {
                        const label = roomFeaturesLabels[feature];
                        return (
                          <li key={idx} className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            {label}
                          </li>
                        );
                      })}
                    </ul>
                  </div>
                </div>
              </div>
            )}
            {/* Preferences */}
            <div className="space-y-7 bg-white shadow p-5 rounded-md">
              <div className="space-y-3">
                <h2 className="text-[22px] font-semibold text-secondary flex items-center gap-2.5">
                  <DynamicIcon name="sliders-horizontal" size={24} />{" "}
                  {isPrivateRoom ? "Roommate" : "Guest"} Preferences
                </h2>
                <ul className="text-gray-600 grid grid-cols-1 gap-4">
                  {isPrivateRoom && (
                    <>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Gender Identity:{" "}
                        {listing?.preferredGenderIdentity
                          ?.map((item) => preferredGenderIdentityLabels[item])
                          .join(", ")}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Sexual Orientation:{" "}
                        {listing?.preferredSexualOrientation
                          ?.map((item) => sexualOrientationsLabel[item])
                          .join(", ")}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Age Range:{" "}
                        {listing?.preferredAgeRange?.map((item) => ageRangesLabel[item]).join(", ")}
                      </li>
                    </>
                  )}
                  <li className="flex items-center gap-[10px]">
                    <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                      <DynamicIcon name="check" size={15} />
                    </span>{" "}
                    Ideal {isPrivateRoom ? "Roommate" : "Guest"}:{" "}
                    {isPrivateRoom
                      ? listing?.idealRoommateDescription
                      : listing?.idealTenantDescription}
                  </li>
                </ul>
              </div>
            </div>
            {/* Location */}
            <div className="space-y-7 bg-white shadow p-5 rounded-md">
              <div className="space-y-5">
                <h2 className="text-[22px] font-semibold text-secondary flex items-center gap-2.5">
                  <DynamicIcon name="map-pin" size={24} /> Location
                </h2>
                <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
                  {listing?.neighborhood.map((neighborhood) => {
                    const label = neighborhoodLabels[neighborhood];
                    return (
                      <li key={neighborhood} className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        {label}
                      </li>
                    );
                  })}
                </ul>
                <GoogleMapsProvider>
                  <PropertyLocationMap center={center} listing={listing} />
                </GoogleMapsProvider>
              </div>
            </div>
          </div>
          <div className="col-span-12 lg:col-span-4 2xl:col-span-3">
            <ProfileCard user={user as UserProfile} />
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExploreSpacesDetailsPageSection;
