import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import RedirectAfter from "./_components/RedirectAfter";

interface Args {
  searchParams: Promise<{ session_id: string }>;
}

const MembershipSuccessPage = async ({ searchParams }: Args) => {
  const { session_id } = await searchParams;

  if (!session_id || !session_id.startsWith("cs_")) {
    return <div>Invalid Session</div>;
  }

  return (
    <section>
      <div className="container">
        <div className="text-center">
          <Card className="w-full max-w-lg mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl text-secondary font-semibold">
                Payment Successful
              </CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-700 text-base leading-relaxed">
                Your payment has been successfully processed. You will be redirected to your
                dashboard shortly.
              </p>
            </CardContent>
          </Card>
          <RedirectAfter />
        </div>
      </div>
    </section>
  );
};

export default MembershipSuccessPage;
