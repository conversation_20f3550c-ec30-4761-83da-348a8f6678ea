"use client";

import React from "react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { ChevronsRight } from "lucide-react";
import formatSegment from "@/utils/formatSegment";

type BreadCrumbProps = {
  overrides?: { [key: string]: string };
  showHome?: boolean;
  homeHref?: string;
  className?: string;
};

const BreadCrumberNav = ({
  overrides = {},
  showHome = false,
  homeHref = "/",
  className = "",
}: BreadCrumbProps) => {
  const pathname = usePathname();

  if (pathname === "/") return null;

  const segments = pathname.split("/").filter((segment: string) => segment !== "");

  const breadcrumbs = segments.map(
    (
      segment: string,
      index: number,
    ): {
      href: string;
      label: string;
      isLast: boolean;
    } => {
      // Create path up to current segment
      const path = `/${segments.slice(0, index + 1).join("/")}`;

      const displayName = overrides[segment] || formatSegment(segment);

      return {
        href: path,
        label: displayName,
        isLast: index === segments.length - 1,
      };
    },
  );
  return (
    <Breadcrumb className={className}>
      <BreadcrumbList>
        {showHome && (
          <>
            <BreadcrumbItem>
              <BreadcrumbLink asChild>
                <Link href={homeHref}>Home</Link>
              </BreadcrumbLink>
            </BreadcrumbItem>
            <BreadcrumbSeparator>
              <ChevronsRight className="h-4 w-4" />
            </BreadcrumbSeparator>
          </>
        )}

        {breadcrumbs.map((crumb, idx: number) => (
          <React.Fragment key={idx}>
            <BreadcrumbItem>
              {crumb.isLast ? (
                <BreadcrumbPage>{crumb.label}</BreadcrumbPage>
              ) : (
                <BreadcrumbLink asChild>
                  <Link href={crumb.href}>{crumb.label}</Link>
                </BreadcrumbLink>
              )}
            </BreadcrumbItem>
            {!crumb.isLast && (
              <BreadcrumbSeparator>
                <ChevronsRight className="h-4 w-4" />
              </BreadcrumbSeparator>
            )}
          </React.Fragment>
        ))}
      </BreadcrumbList>
    </Breadcrumb>
  );
};

export default BreadCrumberNav;
