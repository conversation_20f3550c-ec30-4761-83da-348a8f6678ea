"use client";

import React from "react";
import Link from "next/link";
import MessageItem from "./MessageItem";
import { Conversation } from "@/typescript/interfaces";
import { AuthStore, authStore } from "@/store/authStore";

interface MessageListProps {
  conversation: Conversation;
}

const MessageList = ({ conversation }: MessageListProps) => {
  const { user } = authStore((state: AuthStore) => state);

  if (!user) return null;

  const recipients = conversation.members.find((member) => member.user._id !== user?._id);

  return (
    <Link
      key={recipients?._id}
      href={`/dashboard/messages/${conversation._id}`}
      className="block border p-3 rounded-sm bg-white"
    >
      <MessageItem member={recipients?.user} />
    </Link>
  );
};

export default MessageList;
