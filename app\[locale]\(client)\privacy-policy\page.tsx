import React from "react";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import { PrivacyPolicy, TermsOfUse } from "@/typescript/types";
import { getTranslations } from "next-intl/server";

const PrivacyPolicyPage = async () => {
  const t = await getTranslations("PrivacyPolicyPage");
  const title = t("title");
  const description = t.raw("description");
  const privacyPolicyContents = t.raw("contents");

  return (
    <>
      <TopBanner title={title} />
      <section className="py-[40px] lg:py-[60px]">
        <div className="container">
          <div className="space-y-2 text-[#555555]">
            {description.map((desc: string, idx: number) => (
              <p key={idx} className="text-base lg:text-[22px]">
                {desc}
              </p>
            ))}
          </div>
          <hr className="my-5" />
          <div className="text-[#555555] space-y-5">
            {privacyPolicyContents.map((content: PrivacyPolicy, idx: number) => (
              <PrivacyPolicyContent key={idx} {...content} />
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

const PrivacyPolicyContent = ({ title, contents }: TermsOfUse) => {
  return (
    <div className="space-y-3">
      <h3 className="text-base lg:text-[22px] font-semibold mb-[30px]">{title}</h3>
      <ul className="space-y-[30px]">
        {contents.map((content: string, idx: number) => (
          <li key={idx} className="text-base lg:text-[22px]">
            {content}
          </li>
        ))}
      </ul>
    </div>
  );
};

export default PrivacyPolicyPage;
