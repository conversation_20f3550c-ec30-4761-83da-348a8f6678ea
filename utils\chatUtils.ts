import { Message, UserProfile } from "@/typescript/interfaces";

/**
 * Utility functions for chat system user identification and message handling
 */

/**
 * Determines if a message is from the current user
 * @param message - The message object
 * @param currentUserId - The current user's ID
 * @returns boolean indicating if the message is from the current user
 */
export const isMessageFromCurrentUser = (message: Message, currentUserId: string): boolean => {
  // Handle different possible structures of the user field
  if (typeof message.user === 'string') {
    return message.user === currentUserId;
  }
  
  if (typeof message.user === 'object' && message.user !== null) {
    // If user is an object with _id property
    return (message.user as any)._id === currentUserId;
  }
  
  // Fallback to senderUser field if available
  if (message.senderUser) {
    if (typeof message.senderUser === 'string') {
      return message.senderUser === currentUserId;
    }
    if (typeof message.senderUser === 'object') {
      return (message.senderUser as any)._id === currentUserId;
    }
  }
  
  return false;
};

/**
 * Extracts user ID from various user object structures
 * @param user - User object or string
 * @returns string user ID
 */
export const extractUserId = (user: string | UserProfile | any): string => {
  if (typeof user === 'string') {
    return user;
  }
  
  if (typeof user === 'object' && user !== null) {
    return user._id || user.id || '';
  }
  
  return '';
};

/**
 * Normalizes message data to ensure consistent user identification
 * @param message - Raw message from API
 * @param currentUserId - Current user's ID
 * @returns Normalized message object
 */
export const normalizeMessage = (message: any, currentUserId: string): Message => {
  return {
    ...message,
    // Ensure user field is always a string ID for consistent comparison
    user: extractUserId(message.user) || currentUserId,
    // Preserve original user object for display purposes
    senderUser: message.user,
  };
};

/**
 * Groups messages by sender for better chat display
 * @param messages - Array of messages
 * @param currentUserId - Current user's ID
 * @returns Grouped messages with sender information
 */
export const groupMessagesBySender = (messages: Message[], currentUserId: string) => {
  const grouped: Array<{
    isCurrentUser: boolean;
    messages: Message[];
    sender: string;
  }> = [];
  
  let currentGroup: Message[] = [];
  let currentSender: string | null = null;
  
  messages.forEach((message) => {
    const messageSender = extractUserId(message.user);
    
    if (messageSender !== currentSender) {
      // Start a new group
      if (currentGroup.length > 0) {
        grouped.push({
          isCurrentUser: currentSender === currentUserId,
          messages: currentGroup,
          sender: currentSender || '',
        });
      }
      currentGroup = [message];
      currentSender = messageSender;
    } else {
      // Add to current group
      currentGroup.push(message);
    }
  });
  
  // Add the last group
  if (currentGroup.length > 0) {
    grouped.push({
      isCurrentUser: currentSender === currentUserId,
      messages: currentGroup,
      sender: currentSender || '',
    });
  }
  
  return grouped;
};

/**
 * Formats user display name from user object
 * @param user - User object
 * @returns Formatted display name
 */
export const formatUserDisplayName = (user: UserProfile | any): string => {
  if (!user) return 'Unknown User';
  
  const firstName = user.firstName || '';
  const lastName = user.lastName || '';
  
  return `${firstName} ${lastName}`.trim() || user.email || 'Unknown User';
};

/**
 * Gets user avatar URL with fallback
 * @param user - User object
 * @param cdnUrl - CDN base URL
 * @returns Avatar URL or fallback
 */
export const getUserAvatarUrl = (user: UserProfile | any, cdnUrl: string): string => {
  if (!user?.avatar) {
    return '/default-avatar.png'; // Fallback avatar
  }
  
  const { filePath, fileName } = user.avatar;
  return `${cdnUrl}/${filePath}/${fileName}`;
};
