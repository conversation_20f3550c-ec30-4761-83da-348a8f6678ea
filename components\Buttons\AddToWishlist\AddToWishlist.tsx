"use client";

import React, { useEffect } from "react";
import { Button } from "@/components/ui/button";
import { FaRegHeart, FaHeart } from "react-icons/fa";
import toast from "react-hot-toast";
import useFavoriteStore from "@/store/useFavoriteStore";
import { authStore, AuthStore } from "@/store/authStore";

interface AddToWishlistProps {
  className?: string;
  listingId: string;
}

const AddToWishlist = ({ className, listingId }: AddToWishlistProps) => {
  const { isAuthenticated } = authStore((state: AuthStore) => state);
  const { favorites, addToFavorite, removeFromFavorite, getFavorites } = useFavoriteStore();
  const isFavorite = favorites.some((fav) => fav._id === listingId);

  useEffect(() => {
    if (!isAuthenticated) return;

    getFavorites();
  }, [getFavorites, isAuthenticated]);

  const handleToggle = async () => {
    try {
      if (!isAuthenticated) {
        toast.error("Please login to continue");
        return;
      }
      if (isFavorite) {
        await removeFromFavorite(listingId);
      } else {
        await addToFavorite(listingId);
      }
    } catch (error: any) {
      toast.error(error?.message || "Something went wrong, Please login to continue");
    }
  };

  return (
    <Button variant="outline" size="icon" className={className} onClick={handleToggle}>
      {isFavorite ? (
        <FaHeart size={20} color="#E74C3C" />
      ) : (
        <FaRegHeart size={20} color="#555555" />
      )}
    </Button>
  );
};

export default AddToWishlist;
