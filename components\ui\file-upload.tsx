/**
 * v0 by Vercel.
 * @see https://v0.dev/t/SzicctxPeO8
 * Documentation: https://v0.dev/docs#integrating-generated-code-into-your-nextjs-app
 */
"use client";

import { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { X } from "lucide-react";
import { AiOutlineCloudUpload } from "react-icons/ai";
import Image from "next/image";

interface FileUploadProps {
  onFileChange?: (file: File) => void;
  onMultipleFilesChange?: (files: File[]) => void; // New prop for multiple files
  multiple?: boolean;
  maxFiles?: number;
}

export default function FileUpload({
  onFileChange,
  onMultipleFilesChange,
  multiple = false,
  maxFiles = 5,
}: FileUploadProps) {
  const [files, setFiles] = useState<File[]>([]);

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (!e.target.files?.length) return;

    if (multiple) {
      const newFiles = Array.from(e.target.files);
      const updatedFiles = [...files, ...newFiles].slice(0, maxFiles);
      setFiles(updatedFiles);

      // Call onMultipleFilesChange with all files if provided
      if (onMultipleFilesChange) {
        onMultipleFilesChange(updatedFiles);
      }

      // Call onFileChange with the first file for backward compatibility
      if (newFiles.length > 0) {
        onFileChange?.(newFiles[0]);
      }
    } else {
      const file = e.target.files[0];
      setFiles([file]);
      onFileChange?.(file);
    }
  };

  const removeFile = (index: number) => {
    const updatedFiles = [...files];
    updatedFiles.splice(index, 1);
    setFiles(updatedFiles);

    // Call onMultipleFilesChange with updated files if provided
    if (multiple && onMultipleFilesChange) {
      onMultipleFilesChange(updatedFiles);
    }

    // Call onFileChange with the first remaining file or null
    if (updatedFiles.length > 0) {
      onFileChange?.(updatedFiles[0]);
    }
  };

  const handleSubmit = (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault();
  };

  return (
    <Card className="border-none shadow-none">
      <CardContent>
        <form onSubmit={handleSubmit} className="grid gap-4">
          <div className="flex items-center justify-center w-full">
            {files.length > 0 ? null : (
              <label
                htmlFor="dropzone-file"
                className="flex flex-col items-center justify-center w-full h-64 border border-[#8E44AD] border-dashed rounded-lg cursor-pointer bg-white dark:hover:bg-bray-800 dark:bg-gray-700 hover:bg-gray-100 dark:border-gray-600 dark:hover:border-gray-500 dark:hover:bg-gray-600"
              >
                <div className="flex flex-col items-center justify-center pt-5 pb-6">
                  {/* <UploadIcon className="w-10 h-10 text-gray-400" /> */}
                  <AiOutlineCloudUpload size={60} color="#1C7BBA" />
                  <p className="mt-[20px] text-[18px] text-[#555555]">
                    Drag and Drop or Click to Upload Your Space Photos
                  </p>
                </div>
                <input
                  id="dropzone-file"
                  type="file"
                  className="hidden"
                  onChange={handleFileChange}
                  multiple={multiple}
                  accept="image/*"
                />
              </label>
            )}
          </div>

          {files.length > 0 && (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {files.map((file, index) => {
                const prevURL = URL.createObjectURL(file);
                return (
                  <div key={index} className="p-2 border rounded relative">
                    <Image
                      src={prevURL}
                      alt={file.name}
                      width={500}
                      height={500}
                      className="w-full h-full object-cover rounded-lg"
                    />
                    <Button
                      variant="ghost"
                      size="icon"
                      onClick={() => removeFile(index)}
                      type="button"
                      className="absolute top-2 right-2"
                    >
                      <X className="h-4 w-4" />
                    </Button>
                  </div>
                );
              })}
            </div>
          )}
        </form>
      </CardContent>
    </Card>
  );
}
