"use client";

import React, { useState } from "react";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import RentalPreferencesForm from "./RentalPreferencesForm";
import { UserProfile } from "@/typescript/interfaces";
import {
  allowedPetsLabels,
  furnishedLabels,
  rentalAggreementLabel,
} from "@/constants/listing.constants";
import { parkingRequiresLabel } from "@/constants/constants";

interface Args {
  user: UserProfile;
}

const RentalPreferencesSection = ({ user }: Args) => {
  const [isEdit, setIsEdit] = useState(false);
  return isEdit ? (
    <RentalPreferencesForm isEdit={isEdit} setIsEdit={setIsEdit} user={user} />
  ) : (
    <div className="space-y-7 bg-white shadow p-4 rounded-md border">
      <div className="space-y-3 relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="sliders-horizontal" size={25} /> Rental Preferences
        </h2>
      </div>
      <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Preferred Location: {user?.preferredLocationLabel || "N/A"}
        </li>
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Furnished: {furnishedLabels[user?.wantFurnished ?? ""] || "N/A"}
        </li>
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Rental Duration: {user?.rentalDuration || "N/A"}
        </li>
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Bedrooms: {user?.bedrooms || "N/A"}
        </li>
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Start Date: {user?.rentalStartDate || "N/A"}
        </li>
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Bathrooms: {user?.bathrooms || "N/A"}
        </li>
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Budget: {`$ ${user?.maxMonthlyBudget}` || "N/A"}
        </li>
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Pets: {allowedPetsLabels[user?.pets ?? ""] || "N/A"}
        </li>
        <li className="flex items-center gap-[10px]">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Rental Agreement:{" "}
          {rentalAggreementLabel[user?.willingToSignRentalAgreement ?? ""] || "N/A"}
        </li>
        <li className="flex items-center gap-[10px] capitalize">
          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
            <DynamicIcon name="check" size={15} />
          </span>{" "}
          Parking Required: {parkingRequiresLabel[user?.parkingRequired ?? ""] || "N/A"}
        </li>
      </ul>
    </div>
  );
};

export default RentalPreferencesSection;
