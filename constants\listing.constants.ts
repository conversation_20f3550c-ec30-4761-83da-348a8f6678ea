export const amenitiesLabel: { [key: string]: string } = {
  wifi: "Wifi",
  tv: "TV",
  smart_tv: "Smart TV",
  streaming_device: "Streaming Device",
  cable_satellite: "Cable/Satellite",
  alarm_system: "Alarm System",
  air_conditioning: "Air Conditioning",
  heating: "Heating",
  central_air: "Central Air",
  laundry_on_site: "Laundry On-Site",
  laundry_in_unit: "Laundry In-Unit",
  clothes_washer: "Clothes Washer",
  clothes_dryer: "Clothes Dryer",
  dishwasher: "Dishwasher",
  fireplace: "Fireplace",
  balcony_patio: "Balcony/Patio",
  elevator: "Elevator",
  storage: "Storage",
  doorman: "Doorman",
  pool: "Pool",
  tennis_court: "Tennis Court",
  gym: "Gym",
  hot_tub_jacuzzi: "Hot tub/Jacuzzi",
  bbq_grill: "BBQ Grill",
  yard: "Yard",
  wheelchair_accessible: "Wheelchair accessible",
  maid_cleaning_service: "Maid/Cleaning Service",
};

export const parkingsLabels: { [key: string]: string } = {
  assigned_covered: "Assigned Covered",
  assigned_uncovered: "Assigned Uncovered",
  on_street_permit: "On-street (permit)",
  on_street_no_permit: "On-street (no permit)",
  no_parking_available: "No parking Available",
};

export const utilitiesIncludedLabels: { [key: string]: string } = {
  yes_all: "Yes (All)",
  yes_some: "Yes (Some)",
  no: "No",
};

export const furnishedLabels: { [key: string]: string } = {
  y: "Yes",
  n: "No",
  cf: "Currently furnished, but can be removed",
};

export const allowedPetsLabels: { [key: string]: string } = {
  no_pets: "No Pets",
  dogs: "Dogs",
  cats: "Cats",
  caged_pet: "Caged Pet",
  any_pet: "Any Pet",
};

export const leaseRequiredLabels: { [key: string]: string } = {
  y: "Yes",
  n: "No",
  np: "Not Preference",
};

export const bedroomSizeLabels: { [key: string]: string } = {
  twin: "Twin Bed",
  double: "Double Bed",
  queen: "Queen Bed",
  king: "King Bed",
  np: "No Preference",
};

export const brightnessLabels: { [key: string]: string } = {
  "0": "Very Bright",
  "1": "Moderately bright",
  "2": "Somewhat bright",
  "3": "Not bright",
};

export const bathRoomLabels: { [key: string]: string } = {
  m: "Master (in-room)",
  p: "Private",
  s: "Shared",
  np: "No Preference",
};

export const roomFeaturesLabels: { [key: string]: string } = {
  tv: "TV",
  smart_tv: "Smart TV",
  streaming_device: "Streaming Device",
  cable_satellite_hookup: "Cable/Satellite Hookup",
  dedicated_workspace: "Dedicated Workspace",
  ceiling_fan: "Ceiling Fan",
  carpet: "Carpet",
  hardwood_floor: "Hardwood Floor",
  closet: "Closet",
  walk_in_closet: "Walk-in Closet",
  private_entrance: "Private Entrance",
  balcony_patio: "Balcony/Patio",
  natural_day_light: "Natural Day Light",
  windows: "Window(s)",
};

export const neighborhoodLabels: { [key: string]: string } = {
  gym: "Gym",
  beach: "Beach",
  nightlife: "Nightlife",
  parks: "Parks",
  dog_parks: "Dog Parks",
  airport: "Airport",
  freeway_access: "Freeway access",
  restaurants: "Restaurants",
  cafes_coffee_shop: "Cafes/coffee shop",
  shopping: "Shopping",
  movie_theaters: "Movie Theaters",
  college_university: "College/University",
  public_transport: "Public Transport",
};

export const requiredReferencesLabels: { [key: string]: string } = {
  n: "None",
  1: "1",
  2: "2",
};

export const preferredGenderIdentityLabels: { [key: string]: string } = {
  m: "Male",
  f: "Female",
  tm: "Trans Male",
  tf: "Trans Female",
  nb: "Non Binary",
  o: "Other",
};

export const sexualOrientationsLabel: { [key: string]: string } = {
  g: "Gay",
  l: "Lesbian",
  b: "Bisexual",
  gfs: "Gay Friendly Straight",
};

export const bedRoomsizeLabel: { [key: string]: string } = {
  tb: "Twin Bed",
  db: "Double Bed",
  qb: "Queen Bed",
  kb: "King Bed",
  np: "No Preference",
};

export const bedRoomsLabel: { [key: string]: string } = {
  studio: "Studio",
  "1": "1 Bedroom",
  "2": "2 Bedrooms",
  "3": "3 Bedrooms",
  "4+": "4+ Bedrooms",
};

export const userIntentLabel: { [key: string]: string } = {
  find: "Looking For Space",
  rent: "Has Available Space",
};

export const workFromHomeLabel: { [key: string]: string } = {
  y: "Yes",
  n: "No",
  s: "Sometimes",
};

export const tranvelsLabel: { [key: string]: string } = {
  f: "Frequently",
  o: "Occasionally",
  r: "Rarely",
  n: "Never",
};

export const cleanlinessLabel: { [key: string]: string } = {
  vi: "Very Important",
  i: "Important",
  si: "Somewhat Important",
  nti: "Not too important",
};

export const zodiacSignLabel: { [key: string]: string } = {
  a: "Aries",
  t: "Taurus",
  g: "Gemini",
  c: "Cancer",
  l: "Leo",
  v: "Virgo",
  li: "Libra",
  s: "Scorpio",
  sa: "Sagittarius",
  ca: "Capricorn",
  aq: "Aquarius",
  p: "Pisces",
};

export const describeMyselfAsLabel: { [key: string]: string } = {
  fj: "Fitness Junkie",
  no: "Night Owl",
  eb: "Early Bird",
  f: "Foodie",
  bw: "Bookworm",
  pbg: "Party boy/girl",
  p: "Professional",
  s: "Student",
  t: "Traveler",
  hb: "Homebody",
  sl: "Sports lover",
  al: "Animal lover",
};

export const allowedGuestsLabel: { [key: string]: string } = {
  anytime: "Anytime",
  occasionally: "Occasionally",
  weekends_only: "Weekends only",
  never: "Never",
};

export const smokingHabitsLabel: { [key: string]: string } = {
  y: "Yes",
  n: "No",
  o: "Only Outside",
  pns: "Prefers Not to Say",
  ns: "Nonsmoker",
  nsc: "Nonsmoker (cigarettes)",
  nsm: "Nonsmoker (marijuana)",
  cso: "Cigarettes smoker OK",
  mso: "Marijuana smoker OK",
  np: "No Preference",
};

export const rentalAggreementLabel: { [key: string]: string } = {
  yes: "Yes",
  no: "No",
  not_preference: "Not Preference",
};

export const parkingRequiredLabel: { [key: string]: string } = {
  y: "Yes",
  n: "No",
  np: "Not Preference",
};

export const ageRangesLabel: { [key: string]: string } = {
  "20": "20's",
  "30": "30's",
  "40": "40's",
  "50": "50's",
  "60": "60's",
  "70": "70's",
  np: "No Preference",
};

export const SpaceTypeLabels: { [key: string]: string } = {
  entire_place: "Entire Place",
  private_room: "Private Room",
};

export const bedroomSizeLabel: { [key: string]: string } = {
  tb: "Twin Bed",
  db: "Double Bed",
  qb: "Queen Bed",
  kb: "King Bed",
  np: "No Preference",
};

export const timeDurationLabels: { [key: string]: string } = {
  1: "1 Month",
  2: "2 Months",
  3: "3 Months",
  4: "4 Months",
  5: "5 Months",
  6: "6 Months",
  7: "7 Months",
  8: "8 Months",
  9: "9 Months",
  10: "10 Months",
  11: "11 Months",
  12: "1 Year",
  13: "1 Year +",
  np: "No Preference",
};
