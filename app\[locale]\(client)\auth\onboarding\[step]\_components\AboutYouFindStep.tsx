"use client";

import React, { useState } from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AboutYouFindData, aboutYouFindSchema } from "@/lib/validations/sign-up-schema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  cleanliness,
  describeMyselfAs,
  fluentLanguages,
  geneders,
  sexualOrientations,
  smokeCigarettes,
  smokeMarijuana,
  travels,
  workFromHome,
  zodiacs,
} from "@/constants/constants";
import { Slider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import { MultiSelect } from "@/components/ui/MultiSelect";

const AboutYouFindStep = () => {
  const { data, setUserData, currentStep, steps, markStepCompleted } = useSignupStore();
  const [age, setAge] = useState<number>(data.user.age || 0);
  const form = useForm<AboutYouFindData>({
    resolver: zodResolver(aboutYouFindSchema),
    defaultValues: {
      genderIdentity: data.user.genderIdentity || "",
      sexualOrientation: data.user.sexualOrientation || "",
      age: data.user.age || 0,
      smokeCigarettes: data.user.smokeCigarettes || "",
      smokeMarijuana: data.user.smokeMarijuana || "",
      workFromHome: data.user.workFromHome || "",
      travel: data.user.travel || "",
      cleanliness: data.user.cleanliness || "",
      describeMyselfAs: data.user.describeMyselfAs || [],
      zodiac: data.user.zodiac || "",
      selfDescription: data.user.selfDescription || "",
      fluentLanguages: data.user.fluentLanguages || [],
    },
  });
  const router = useRouter();

  function onSubmit(values: AboutYouFindData) {
    setUserData(values);
    markStepCompleted(currentStep);
    router.push(`/auth/onboarding/${1}`);
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (!form.formState.isValid) return;
    if (currentStep === steps.length - 1) return;
    markStepCompleted(currentStep);
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="genderIdentity"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Gender Identity<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {geneders.map((gender, idx) => (
                    <SelectItem key={idx} value={gender.value}>
                      {gender.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="sexualOrientation"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Sexual Orientation<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {sexualOrientations.map((sexualOrientation, idx) => (
                    <SelectItem key={idx} value={sexualOrientation.value}>
                      {sexualOrientation.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="age"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Age<span className="block md:hidden">: {field.value}</span>
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl className="mt-2">
                <Slider
                  min={18}
                  step={1}
                  max={100}
                  value={[field.value || age]}
                  onValueChange={(value) => {
                    setAge(value[0]);
                    field.onChange(value[0]);
                  }}
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeCigarettes"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Smoke Cigarettes<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeCigarettes.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeMarijuana"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Smoke Marijuana<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeMarijuana.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="workFromHome"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Work From Home<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {workFromHome.map((work, idx) => (
                    <SelectItem key={idx} value={work.value}>
                      {work.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="travel"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Travel<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {travels.map((travel, idx) => (
                    <SelectItem key={idx} value={travel.value}>
                      {travel.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="cleanliness"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Cleanliness<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {cleanliness.map((clean, idx) => (
                    <SelectItem key={idx} value={clean.value}>
                      {clean.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="fluentLanguages"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Fluent Languages<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={fluentLanguages}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="describeMyselfAs"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                I Describe Myself As...<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={describeMyselfAs}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="zodiac"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">Zodiac</FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {zodiacs.map((zodiac, idx) => (
                    <SelectItem key={idx} value={zodiac.value}>
                      {zodiac.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="selfDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Self Description<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Tell us about yourself..." {...field} className="h-40" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button
            type="submit"
            onClick={handleNext}
            disabled={currentStep === steps.length - 1}
            className="flex-1 !py-6 cursor-pointer"
          >
            {currentStep === steps.length - 1 ? "Complete" : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default AboutYouFindStep;
