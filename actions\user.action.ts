const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const updateUserProfile = async (data: any, token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me/about`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const sendOtpToOldEmail = async (email: string, token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me/email/change`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ email }),
    });
    if (response.ok) {
      const resData = await response.json();
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const verifyOldOtp = async (code: string, token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me/email/change/old/verify`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ code }),
    });
    if (response.ok) {
      const resData = await response.json();
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const verifyNewOtp = async (code: string, token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me/email/change/new/verify`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ code }),
    });
    if (response.ok) {
      const resData = await response.json();
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};
