import { useRouter } from "next/navigation";

// utils/socialRedirect.ts
export const handleSocialRedirect = (
  router: ReturnType<typeof useRouter>,
  provider: string,
  type: string = "connect",
) => {
  const state = encodeURIComponent(JSON.stringify({ provider, type }));
  switch (provider) {
    case "google":
      router.push(
        `https://accounts.google.com/o/oauth2/v2/auth?client_id=${process.env.NEXT_PUBLIC_GOOGLE_CLIENT_ID}&redirect_uri=${process.env.NEXT_PUBLIC_FRONTEND_URL}/verify/social/callback&response_type=code&scope=openid%20email%20profile&access_type=offline&state=${state}`,
      );
      break;
    case "facebook":
      router.push(
        `https://www.facebook.com/v19.0/dialog/oauth?client_id=${process.env.NEXT_PUBLIC_FACEBOOK_APP_ID}&redirect_uri=${process.env.NEXT_PUBLIC_FRONTEND_URL}/verify/social/callback&scope=email,public_profile&response_type=code&state=${state}`,
      );
      break;
    case "instagram":
      console.log("Instagram login initiated");
      break;
    case "x":
      console.log("X (Twitter) login initiated");
      break;
    default:
      console.error("Unknown provider:", provider);
  }
};
