"use client";

import React from "react";
import FileUpload from "@/components/ui/file-upload";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { UserProfile } from "@/typescript/interfaces";
import { TFormMode } from "@/typescript/types";

interface Args {
  mode: TFormMode;
  user?: UserProfile;
}

const ProfilePhotoStep = ({ mode }: Args) => {
  const router = useRouter();
  const { currentStep, steps, setUserData, setCurrentStep } = useSignupStore();
  const locationURL = mode === "add" ? "/dashboard/my-spaces/add" : "/dashboard/my-spaces/edit";

  const handlePrevious = () => {
    // if (currentStep === 0) {
    //   router.push("/auth/register");
    // }
    setCurrentStep(currentStep - 1);
    router.push(`${locationURL}/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (currentStep === steps.length - 1) return;
    setCurrentStep(currentStep + 1);
    router.push(`${locationURL}/${currentStep + 1}`);
  };

  const handleFileChange = async (file: File) => {
    // console.log("file", file);
    try {
      const baseURL = process.env.NEXT_PUBLIC_API_URL;
      const formData = new FormData();
      formData.append("file", file);
      formData.append("path", "/user/profile");
      const response = await fetch(`${baseURL}/upload/file`, {
        method: "POST",
        body: formData,
      });
      const data = (await response.json())?.payload;
      // console.log("data", data);
      setUserData({
        avatar: {
          originalName: data.originalName,
          fileName: data.fileName,
          fileSize: data.fileSize,
          mimeType: data.mimeType,
          filePath: data.filePath,
        },
      });
    } catch (error: unknown) {
      console.error("Error uploading avatar file:", error);
    }

    // const preview = URL.createObjectURL(file);
    // console.log("preview", preview);
    // // setUserData({ avatar: preview });
  };

  return (
    <div>
      <FileUpload onFileChange={handleFileChange} />
      <div className="flex justify-between mt-8">
        <Button type="button" variant="outline" onClick={handlePrevious}>
          Previous
        </Button>
        <Button type="submit" onClick={handleNext} disabled={currentStep === steps.length - 1}>
          {currentStep === steps.length - 1 ? "Complete" : "Next"}
        </Button>
      </div>
    </div>
  );
};

export default ProfilePhotoStep;
