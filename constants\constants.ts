export const DFEAULT_USER = "/images/default/user.png";

export const geneders: { label: string; value: string }[] = [
  {
    label: "Male",
    value: "m",
  },
  {
    label: "Female",
    value: "f",
  },
  {
    label: "Trans Male",
    value: "tm",
  },
  {
    label: "Trans Female",
    value: "tf",
  },
  {
    label: "Non-binary",
    value: "nb",
  },
  // {
  //   label: "Other",
  //   value: "o",
  // },
];

export const sexualOrientations: { label: string; value: string }[] = [
  {
    label: "Gay",
    value: "g",
  },
  {
    label: "Lesbian",
    value: "l",
  },
  {
    label: "Bi",
    value: "b",
  },
  {
    label: "Gay-friendly Straight",
    value: "gfs",
  },
  {
    label: "Other",
    value: "o",
  },
];

export const smokeCigarettes: { label: string; value: string }[] = [
  {
    label: "Yes",
    value: "y",
  },
  {
    label: "No",
    value: "n",
  },
  {
    label: "Only outside",
    value: "oo",
  },
];

export const smokeMarijuana: { label: string; value: string }[] = [
  {
    label: "Yes",
    value: "y",
  },
  {
    label: "No",
    value: "n",
  },
  {
    label: "Only outside",
    value: "o",
  },
];

export const workFromHome: { label: string; value: string }[] = [
  {
    label: "Yes",
    value: "y",
  },
  {
    label: "No",
    value: "n",
  },
  {
    label: "Sometimes",
    value: "s",
  },
];

export const travels: { label: string; value: string }[] = [
  {
    label: "Frequently",
    value: "f",
  },
  {
    label: "Occationally",
    value: "o",
  },
  {
    label: "Rarely",
    value: "r",
  },
  {
    label: "Never",
    value: "n",
  },
];

export const cleanliness: { label: string; value: string }[] = [
  {
    label: "Very important",
    value: "vi",
  },
  {
    label: "Important",
    value: "i",
  },
  {
    label: "Somewhat important",
    value: "si",
  },
  {
    label: "Not too important",
    value: "nti",
  },
];

export const describeMyselfAs: { label: string; value: string }[] = [
  {
    label: "Fitness Junkie",
    value: "fj",
  },
  {
    label: "Night Owl",
    value: "no",
  },
  {
    label: "Early Bird",
    value: "eb",
  },
  {
    label: "Foodie",
    value: "f",
  },
  {
    label: "Bookworm",
    value: "bw",
  },
  {
    label: "Party boy/girl",
    value: "pbg",
  },
  {
    label: "Professional",
    value: "p",
  },
  {
    label: "Student",
    value: "s",
  },
  {
    label: "Traveler",
    value: "t",
  },
  {
    label: "Homebody",
    value: "hb",
  },
  {
    label: "Sports lover",
    value: "sl",
  },
  {
    label: "Animal lover",
    value: "al",
  },
];

export const zodiacs: { label: string; value: string }[] = [
  {
    label: "Aries",
    value: "a",
  },
  {
    label: "Taurus",
    value: "t",
  },
  {
    label: "Gemini",
    value: "g",
  },
  {
    label: "Cancer",
    value: "c",
  },
  {
    label: "Leo",
    value: "l",
  },
  {
    label: "Virgo",
    value: "v",
  },
  {
    label: "Libra",
    value: "li",
  },
  {
    label: "Scorpio",
    value: "s",
  },
  {
    label: "Sagittarius",
    value: "sa",
  },
  {
    label: "Capricorn",
    value: "ca",
  },
  {
    label: "Aquarius",
    value: "aq",
  },
  {
    label: "Pisces",
    value: "p",
  },
];

export const residenceTypes: { label: string; value: string }[] = [
  {
    label: "Apartment",
    value: "apartment",
  },
  {
    label: "Condo",
    value: "condo",
  },
  {
    label: "Townhouse",
    value: "townhouse",
  },
  {
    label: "House",
    value: "house",
  },
];

export const bedRooms: { label: string; value: string }[] = [
  {
    label: "Studio",
    value: "studio",
  },
  {
    label: "1 Bedroom",
    value: "1",
  },
  {
    label: "2 Bedrooms",
    value: "2",
  },
  {
    label: "3 Bedrooms",
    value: "3",
  },
  {
    label: "4+ Bedrooms",
    value: "4+",
  },
];

export const bathRooms: { label: string; value: string }[] = [
  {
    label: "1 Bathroom",
    value: "1",
  },
  {
    label: "2 Bathrooms",
    value: "2",
  },
  {
    label: "3 Bathrooms",
    value: "3",
  },
  {
    label: "4+ Bathrooms",
    value: "4+",
  },
];

export const ownerOccupied: { label: string; value: string }[] = [
  {
    label: "Yes",
    value: "yes",
  },
  {
    label: "No",
    value: "no",
  },
];

export const numberOfOccupants: { label: string; value: string }[] = [
  {
    label: "1 Occupant",
    value: "1",
  },
  {
    label: "2 Occupants",
    value: "2",
  },
  {
    label: "3 Occupants",
    value: "3",
  },
  {
    label: "4+ Occupants",
    value: "4+",
  },
];

export const amenities: { label: string; value: string }[] = [
  { label: "Wifi", value: "wifi" },
  { label: "TV", value: "tv" },
  { label: "Smart TV", value: "smart_tv" },
  { label: "Streaming Device", value: "streaming_device" },
  { label: "Cable/Satellite", value: "cable_satellite" },
  { label: "Alarm System", value: "alarm_system" },
  { label: "Air Conditioning", value: "air_conditioning" },
  { label: "Heating", value: "heating" },
  { label: "Central Air", value: "central_air" },
  { label: "Laundry On-Site", value: "laundry_on_site" },
  { label: "Laundry In-Unit", value: "laundry_in_unit" },
  { label: "Clothes Washer", value: "clothes_washer" },
  { label: "Clothes Dryer", value: "clothes_dryer" },
  { label: "Dishwasher", value: "dishwasher" },
  { label: "Fireplace", value: "fireplace" },
  { label: "Balcony/Patio", value: "balcony_patio" },
  { label: "Elevator", value: "elevator" },
  { label: "Storage", value: "storage" },
  { label: "Doorman", value: "doorman" },
  { label: "Pool", value: "pool" },
  { label: "Tennis Court", value: "tennis_court" },
  { label: "Gym", value: "gym" },
  { label: "Hot tub/Jacuzzi", value: "hot_tub_jacuzzi" },
  { label: "BBQ Grill", value: "bbq_grill" },
  { label: "Yard", value: "yard" },
  { label: "Wheelchair accessible", value: "wheelchair_accessible" },
  { label: "Maid/Cleaning Service", value: "maid_cleaning_service" },
];

export const parkingOptions: { label: string; value: string }[] = [
  { label: "Assigned Covered", value: "assigned_covered" },
  { label: "Assigned Uncovered", value: "assigned_uncovered" },
  { label: "On-street (no permit)", value: "on_street_no_permit" },
  { label: "No parking Available", value: "no_parking_available" },
];

export const parkingRequires: { label: string; value: string }[] = [
  { label: "Yes", value: "y" },
  { label: "No", value: "n" },
  { label: "Not Preference", value: "np" },
];

export const parkingRequiresLabel: { [key: string]: string } = {
  y: "Yes",
  n: "No",
  np: "Not Preference",
};

export const neighborhood: { label: string; value: string }[] = [
  { label: "Gym", value: "gym" },
  { label: "Beach", value: "beach" },
  { label: "Nightlife", value: "nightlife" },
  { label: "Parks", value: "parks" },
  { label: "Dog Parks", value: "dog_parks" },
  { label: "Airport", value: "airport" },
  { label: "Freeway access", value: "freeway_access" },
  { label: "Restaurants", value: "restaurants" },
  { label: "Cafes/coffee shop", value: "cafes_coffee_shop" },
  { label: "Shopping", value: "shopping" },
  { label: "Movie Theaters", value: "movie_theaters" },
  { label: "College/University", value: "college_university" },
  { label: "Public Transport", value: "public_transport" },
];

export const currentPets: { label: string; value: string }[] = [
  { label: "No Pets", value: "no_pets" },
  { label: "Dogs", value: "dogs" },
  { label: "Cats", value: "cats" },
  { label: "Caged pet", value: "caged_pet" },
  { label: "Any Pet", value: "any_pet" },
];

export const allowPets: { label: string; value: string }[] = [
  { label: "No Pets", value: "no_pets" },
  { label: "Dogs", value: "dogs" },
  { label: "Cats", value: "cats" },
  { label: "Caged pet", value: "caged_pet" },
  { label: "Any Pet", value: "any_pet" },
];

export const allowedGuests: { label: string; value: string }[] = [
  { label: "Anytime", value: "anytime" },
  { label: "Occasionally", value: "occasionally" },
  { label: "Weekends only", value: "weekends_only" },
  { label: "Never", value: "never" },
];

export const utilitiesIncluded: { label: string; value: string }[] = [
  { label: "Yes (All)", value: "yes_all" },
  { label: "Yes (Some)", value: "yes_some" },
  { label: "No", value: "no" },
];

export const furnished: { label: string; value: string }[] = [
  { label: "Yes", value: "y" },
  { label: "No", value: "n" },
  { label: "Currently furnished, but can be removed", value: "cf" },
];

export const wantFurnished: { label: string; value: string }[] = [
  { label: "Yes", value: "y" },
  { label: "No", value: "n" },
  { label: "No preference", value: "np" },
];

export const bedroomSize: { label: string; value: string }[] = [
  { label: "Twin Bed", value: "tb" },
  { label: "Double Bed", value: "db" },
  { label: "Queen Bed", value: "qb" },
  { label: "King Bed", value: "kb" },
  // { label: "No Preferred", value: "np" },
];

export const brightness: { label: string; value: string }[] = [
  { label: "Very bright", value: "0" },
  { label: "Moderately bright", value: "1" },
  { label: "Somewhat bright", value: "2" },
  { label: "Not bright", value: "3" },
];

export const bathRoom: { label: string; value: string }[] = [
  {
    label: "Master (in-room)",
    value: "m",
  },
  {
    label: "Private",
    value: "p",
  },
  {
    label: "Shared",
    value: "s",
  },
  // {
  //   label: "No Preferred",
  //   value: "np",
  // },
];

export const roomFeatures: { label: string; value: string }[] = [
  { label: "TV", value: "tv" },
  { label: "Smart TV", value: "smart_tv" },
  { label: "Streaming Device", value: "streaming_device" },
  { label: "Cable/Satellite Hookup", value: "cable_satellite_hookup" },
  { label: "Dedicated Workspace", value: "dedicated_workspace" },
  { label: "Ceiling Fan", value: "ceiling_fan" },
  { label: "Carpet", value: "carpet" },
  { label: "Hardwood Floor", value: "hardwood_floor" },
  { label: "Closet", value: "closet" },
  { label: "Walk-in Closet", value: "walk_in_closet" },
  { label: "Private Entrance", value: "private_entrance" },
  { label: "Balcony/Patio", value: "balcony_patio" },
  { label: "Natural Day Light", value: "natural_day_light" },
  { label: "Window(s)", value: "windows" },
];

export const parkings: { label: string; value: string }[] = [
  { label: "Assigned Covered", value: "assigned_covered" },
  { label: "Assigned Uncovered", value: "assigned_uncovered" },
  { label: "On-street (permit)", value: "on_street_permit" },
  { label: "On-street (no permit)", value: "on_street_no_permit" },
  { label: "No parking Available", value: "no_parking_available" },
];

export const neighthborhoods: { label: string; value: string }[] = [
  { label: "Gym", value: "gym" },
  { label: "Beach", value: "beach" },
  { label: "Nightlife", value: "nightlife" },
  { label: "Parks", value: "parks" },
  { label: "Dog Parks", value: "dog_parks" },
  { label: "Airport", value: "airport" },
  { label: "Freeway access", value: "freeway_access" },
  { label: "Restaurants", value: "restaurants" },
  { label: "Cafes/coffee shop", value: "cafes_coffee_shop" },
  { label: "Shopping", value: "shopping" },
  { label: "Movie Theaters", value: "movie_theaters" },
  { label: "College/University", value: "college_university" },
  { label: "Public Transport", value: "public_transport" },
];

export const ageRanges: { label: string; value: string }[] = [
  { label: "20's", value: "20" },
  { label: "30's", value: "30" },
  { label: "40's", value: "40" },
  { label: "50's", value: "50" },
  { label: "60's", value: "60" },
  { label: "70's", value: "70" },
  { label: "No Preference", value: "np" },
];

export const fluentLanguages: { label: string; value: string }[] = [
  { label: "English", value: "english" },
  { label: "Spanish", value: "spanish" },
  { label: "French", value: "french" },
  { label: "Italian", value: "italian" },
  { label: "German", value: "german" },
  { label: "Portuguese", value: "portuguese" },
  { label: "Greek", value: "greek" },
  { label: "Chinese (Mandarin)", value: "chinese" },
  { label: "Japanese", value: "japanese" },
];

export const willingToSignRentalAgreement: { label: string; value: string }[] = [
  { label: "Yes", value: "yes" },
  { label: "No", value: "no" },
];

export const spaceTypes: { label: string; value: string }[] = [
  { label: "Entire Place", value: "entire_place" },
  { label: "Private Room", value: "private_room" },
];

export const userType: { label: string; value: string }[] = [
  {
    label: "Looking For Space",
    value: "find",
  },
  {
    label: "Has Available Space",
    value: "rent",
  },
  {
    label: "Both",
    value: "both",
  },
];

// Public Routes
export const PUBLIC_PATHS = ["/auth"];

export const roomTypeLabels: { [key: string]: string } = {
  private_room: "Private Room",
  entire_place: "Entire Place",
};

export const amenitiesLabel: Record<string, string> = {
  wifi: "Wifi",
  tv: "TV",
  smart_tv: "Smart TV",
  streaming_device: "Streaming Device",
  cable_satellite: "Cable/Satellite",
  alarm_system: "Alarm System",
  air_conditioning: "Air Conditioning",
  heating: "Heating",
  central_air: "Central Air",
  laundry_on_site: "Laundry On-Site",
  laundry_in_unit: "Laundry In-Unit",
  clothes_washer: "Clothes Washer",
  clothes_dryer: "Clothes Dryer",
  dishwasher: "Dishwasher",
  fireplace: "Fireplace",
  balcony_patio: "Balcony/Patio",
  elevator: "Elevator",
  storage: "Storage",
  doorman: "Doorman",
  pool: "Pool",
  tennis_court: "Tennis Court",
  gym: "Gym",
  hot_tub_jacuzzi: "Hot tub/Jacuzzi",
  bbq_grill: "BBQ Grill",
  yard: "Yard",
  wheelchair_accessible: "Wheelchair accessible",
  maid_cleaning_service: "Maid/Cleaning Service",
};

export const smokingHabits: { label: string; value: string }[] = [
  { label: "Nonsmoker", value: "ns" },
  { label: "Nonsmoker (cigarettes)", value: "nsc" },
  { label: "Nonsmoker (marijuana)", value: "nsm" },
  { label: "Cigarettes smoker OK", value: "cso" },
  { label: "Marijuana smoker OK", value: "mso" },
  { label: "No Preference", value: "np" },
];

export const preferredGenders: { label: string; value: string }[] = [
  {
    label: "Male",
    value: "m",
  },
  {
    label: "Female",
    value: "f",
  },
  {
    label: "Trans Male",
    value: "tm",
  },
  {
    label: "Trans Female",
    value: "tf",
  },
  {
    label: "Non-binary",
    value: "nb",
  },
  {
    label: "No preference",
    value: "np",
  },
];

export const preferredSexualOrientations: { label: string; value: string }[] = [
  {
    label: "Gay",
    value: "g",
  },
  {
    label: "Lesbian",
    value: "l",
  },
  {
    label: "Bi",
    value: "b",
  },
  {
    label: "Gay-friednly Straight",
    value: "gfs",
  },
  {
    label: "No preference",
    value: "np",
  },
];

export const findBathRooms: { label: string; value: string }[] = [
  {
    label: "Master (in-room)",
    value: "m",
  },
  {
    label: "Private",
    value: "p",
  },
  {
    label: "Shared",
    value: "s",
  },
  {
    label: "No Preference",
    value: "np",
  },
];

export const findBedroomSize: { label: string; value: string }[] = [
  { label: "Twin Bed", value: "tb" },
  { label: "Double Bed", value: "db" },
  { label: "Queen Bed", value: "qb" },
  { label: "King Bed", value: "kb" },
  { label: "No Preferred", value: "np" },
];

export const findPets: { label: string; value: string }[] = [
  { label: "No Pets", value: "no_pets" },
  { label: "Dogs", value: "dogs" },
  { label: "Cats", value: "cats" },
  { label: "Caged Pet", value: "caged_pet" },
  { label: "Other Pet", value: "other_pet" },
];

export const findParking: { label: string; value: string }[] = [
  { label: "Yes", value: "y" },
  { label: "No", value: "n" },
];
