"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { RoommatePrefsData, roommatePrefsSchema } from "@/lib/validations/profileForm.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  ageRanges,
  preferredGenders,
  preferredSexualOrientations,
  smokingHabits,
} from "@/constants/constants";
import { updateUserProfile } from "@/actions/user.action";
import toast from "react-hot-toast";
import { UserProfile } from "@/typescript/interfaces";
import { useRouter } from "next/navigation";
import { useCookies } from "react-cookie";
import { MultiSelect } from "@/components/ui/MultiSelect";

interface Args {
  isEdit: boolean;
  setIsEdit: (value: boolean) => void;
  user: UserProfile;
}

const RoommatePreferencesForm = ({ isEdit, setIsEdit, user }: Args) => {
  const [cookies] = useCookies(["token"]);
  const token = cookies.token;
  const router = useRouter();
  const form = useForm<RoommatePrefsData>({
    resolver: zodResolver(roommatePrefsSchema),
    defaultValues: {
      preferredGenderIdentity: user?.preferredGenderIdentity || [],
      preferredSexualOrientation: user?.preferredSexualOrientation || [],
      preferredAgeRange: user?.preferredAgeRange || [],
      preferredSmokingHabits: user?.preferredSmokingHabits || [],
    },
  });

  const onSubmit = async (data: RoommatePrefsData) => {
    const response = await updateUserProfile(data, token);
    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message);
      setIsEdit(!isEdit);
      router.refresh();
    } else {
      toast.error(response.message);
      setIsEdit(!isEdit);
      router.refresh();
    }
    // form.reset();
  };

  return (
    <div className="space-y-7 bg-white shadow p-4 rounded-md border">
      <div className="space-y-3 relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="user-round-search" size={24} /> Roommate Preferences
        </h2>
      </div>
      <Form {...form}>
        <form className="space-y-5" onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            name="preferredGenderIdentity"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Preferred Gender Identity</FormLabel>
                <FormControl>
                  <MultiSelect
                    options={preferredGenders}
                    onValueChange={field.onChange}
                    className="input-field"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="preferredSexualOrientation"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Preferred Sexual Orientation</FormLabel>
                <FormControl>
                  <MultiSelect
                    options={preferredSexualOrientations}
                    onValueChange={field.onChange}
                    className="input-field"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="preferredAgeRange"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Preferred Age Range</FormLabel>
                <FormControl>
                  <MultiSelect
                    options={ageRanges}
                    onValueChange={field.onChange}
                    className="input-field"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="preferredSmokingHabits"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Preferred Smoking Habits</FormLabel>
                <FormControl>
                  <MultiSelect
                    defaultChecked
                    options={smokingHabits}
                    onValueChange={field.onChange}
                    className="input-field"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="w-full max-w-sm mx-auto ">
            <Button type="submit" className="w-full h-auto py-3 rounded-full cursor-pointer">
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default RoommatePreferencesForm;
