import React from "react";
import ProfileCard from "@/components/Cards/ProfileCard/ProfileCard";
import { DynamicIcon } from "lucide-react/dynamic";
import { PropertyData, UserProfile } from "@/typescript/interfaces";
import ListingCard from "@/components/Cards/ListingCard/ListingCard";
import {
  allowedPetsLabels,
  bathRoomLabels,
  bedroomSizeLabel,
  cleanlinessLabel,
  describeMyselfAsLabel,
  furnishedLabels,
  parkingRequiredLabel,
  preferredGenderIdentityLabels,
  rentalAggreementLabel,
  sexualOrientationsLabel,
  smokingHabitsLabel,
  SpaceTypeLabels,
  tranvelsLabel,
  workFromHomeLabel,
  zodiacSignLabel,
} from "@/constants/listing.constants";
import { roomTypeLabels } from "@/constants/constants";
import formatDate from "@/utils/formatDate";
import formattedCurrency from "@/lib/currencyFormatter";

interface ExploreFacesDetailsPageSectionProps {
  user: UserProfile;
  listings: PropertyData[];
}

const ExploreFacesDetailsPageSection = ({
  user,
  listings,
}: ExploreFacesDetailsPageSectionProps) => {
  const isPrivateRoom = user.spaceType === "private_room";
  const showListings =
    user.intent === "rent" &&
    (user.spaceType === "private_room" || user.spaceType === "entire_place");
  const showPersonalDetails = !(user.intent === "rent" && user.spaceType === "entire_place");
  const showRentalPreferences =
    user.intent === "find" &&
    (user.spaceType === "private_room" || user.spaceType === "entire_place");
  const showRoommatePreferences = user.intent === "find" && user.spaceType === "private_room";

  return (
    <section className="pb-[40px]">
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 lg:col-span-4 2xl:col-span-3">
            <ProfileCard user={user} />
          </div>
          <div className="col-span-12 lg:col-span-8 2xl:col-span-9 space-y-[30px]">
            <div className="space-y-7 bg-white shadow p-5 rounded-md">
              <div className="space-y-3">
                <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
                  <DynamicIcon name="user" size={24} /> About {user.firstName || "N/A"}
                </h2>
                <p>{user.selfDescription}</p>
              </div>
              {showPersonalDetails && (
                <>
                  {/* Basic */}
                  <div className="space-y-3">
                    <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                      Lifestyle
                    </h2>
                    <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Work From Home: {workFromHomeLabel[user?.workFromHome ?? ""] || "N/A"}
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Smoke Cigarettes: {smokingHabitsLabel[user?.smokeCigarettes ?? ""] || "N/A"}
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Travel: {tranvelsLabel[user?.travel ?? ""] || "N/A"}
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Smoke Marijuana: {smokingHabitsLabel[user?.smokeMarijuana ?? ""] || "N/A"}
                      </li>
                      <li className="flex items-center gap-2">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Cleanliness: {cleanlinessLabel[user?.cleanliness ?? ""] || "N/A"}
                      </li>
                    </ul>
                  </div>
                  {/* Describe Myself As */}
                  <div className="space-y-3">
                    <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                      I Describe Myself as...
                    </h2>
                    <ul className="text-gray-600 grid grid-cols-1 gap-4">
                      {(user.describeMyselfAs ?? []).map((item, idx) => (
                        <li key={idx} className="flex items-center gap-[10px] capitalize">
                          <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                            <DynamicIcon name="check" size={15} />
                          </span>{" "}
                          {describeMyselfAsLabel[item] || "N/A"}
                        </li>
                      )) || "N/A"}
                    </ul>
                  </div>
                  {/* Fluent Languages */}
                  {user.fluentLanguages && user.fluentLanguages?.length > 0 && (
                    <div className="space-y-3">
                      <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                        Languages
                      </h2>
                      <ul className="text-gray-600 grid grid-cols-1 gap-4">
                        {(user.fluentLanguages ?? []).map((language, idx) => (
                          <li key={idx} className="flex items-center gap-[10px] capitalize">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            {language}
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  {/* Zodiac */}
                  <div className="space-y-3">
                    <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                      Zodiac Sign
                    </h2>
                    <ul className="text-gray-600 grid grid-cols-1 md:grid-cols-2 gap-4">
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        {zodiacSignLabel[user.zodiac ?? ""] || "N/A"}
                      </li>
                    </ul>
                  </div>
                </>
              )}
            </div>
            {showRentalPreferences && (
              <div className="space-y-7 bg-white shadow p-5 rounded-md">
                <div className="space-y-3">
                  <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
                    <DynamicIcon name="sliders-horizontal" size={24} /> Rental Preferences
                  </h2>
                  {isPrivateRoom ? (
                    <div className="space-y-5">
                      <div className="space-y-3">
                        <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                          The Basics
                        </h2>
                        <ul className="text-[#555555] grid grid-cols-1 md:grid-cols-2 gap-4">
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Preferred Location: {user.preferredLocationLabel || "N/A"}
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Start Date: {formatDate(user?.rentalStartDate ?? "") || "N/A"}
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Space Type: {roomTypeLabels[user.spaceType ?? ""] || "N/A"}
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Monthly Budget:{" "}
                            {formattedCurrency.format(user?.maxMonthlyBudget ?? 0) || "N/A"}
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Rental Duration: 7 months
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Sign Lease:{" "}
                            {rentalAggreementLabel[user?.willingToSignRentalAgreement ?? ""]}
                          </li>
                        </ul>
                      </div>
                      <div className="space-y-3">
                        <h2 className="text-xl font-semibold text-primary flex items-center gap-2.5">
                          The Room
                        </h2>
                        <ul className="text-[#555555] grid grid-cols-1 md:grid-cols-2 gap-4">
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Furnished: {furnishedLabels[user?.wantFurnished ?? ""] || "N/A"}
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Pets: {allowedPetsLabels[user?.pets ?? ""] || "N/A"}
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Bathroom: {bathRoomLabels[user?.bathroom ?? ""] || "N/A"}
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Parking Required:{" "}
                            {parkingRequiredLabel[user?.parkingRequired ?? ""] || "N/A"}
                          </li>
                          <li className="flex items-center gap-[10px]">
                            <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                              <DynamicIcon name="check" size={15} />
                            </span>{" "}
                            Bedroom Size: {bedroomSizeLabel[user?.bedroomSize ?? ""] || "N/A"}
                          </li>
                        </ul>
                      </div>
                    </div>
                  ) : (
                    <ul className="text-[#555555] grid grid-cols-1 md:grid-cols-2 gap-4">
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Preferred Location: {user.preferredLocationLabel || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Furnished: {furnishedLabels[user?.wantFurnished ?? ""] || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Space Type: {SpaceTypeLabels[user?.spaceType ?? ""] || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Bedrooms: {user?.bedrooms || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Rental Duration: 6 months
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Bathrooms: {user?.bathrooms || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Start Date: {formatDate(user?.rentalStartDate ?? "") || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Pets: {allowedPetsLabels[user?.pets ?? ""] || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Budget: {formattedCurrency.format(user?.maxMonthlyBudget ?? 0) || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Parking Required:{" "}
                        {parkingRequiredLabel[user?.parkingRequired ?? ""] || "N/A"}
                      </li>
                      <li className="flex items-center gap-[10px]">
                        <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                          <DynamicIcon name="check" size={15} />
                        </span>{" "}
                        Rental Agreement:{" "}
                        {rentalAggreementLabel[user?.willingToSignRentalAgreement ?? ""] || "N/A"}
                      </li>
                    </ul>
                  )}
                </div>
              </div>
            )}
            {showRoommatePreferences && (
              <div className="space-y-7 bg-white shadow p-5 rounded-md">
                <div className="space-y-3">
                  <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
                    <DynamicIcon name="user-search" size={24} /> Roommate Preferences
                  </h2>
                  <ul className="text-[#555555] grid grid-cols-1 gap-4">
                    <li className="flex items-center gap-[10px]">
                      <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                        <DynamicIcon name="check" size={15} />
                      </span>{" "}
                      Gender Identity:{" "}
                      {user?.preferredGenderIdentity
                        ?.map((item) => preferredGenderIdentityLabels[item])
                        .join(", ") || "N/A"}
                    </li>
                    <li className="flex items-center gap-[10px]">
                      <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                        <DynamicIcon name="check" size={15} />
                      </span>{" "}
                      Orientation:{" "}
                      {user?.preferredSexualOrientation
                        ?.map((item) => sexualOrientationsLabel[item])
                        .join(", ") || "N/A"}
                    </li>
                    <li className="flex items-center gap-[10px]">
                      <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                        <DynamicIcon name="check" size={15} />
                      </span>{" "}
                      Age Range: {user?.preferredAgeRange + "'s" || "N/A"}
                    </li>
                    <li className="flex items-center gap-[10px]">
                      <span className="w-5 h-5 p-1 bg-secondary text-white inline-flex justify-center items-center rounded-full">
                        <DynamicIcon name="check" size={15} />
                      </span>{" "}
                      Smoking:{" "}
                      {user?.preferredSmokingHabits
                        ?.map((item) => smokingHabitsLabel[item])
                        .join(", ") || "N/A"}
                    </li>
                  </ul>
                </div>
              </div>
            )}
            {/* Listings */}
            {showListings && (
              <div className="space-y-7 bg-white shadow p-5 rounded-md">
                <div className="space-y-3">
                  <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
                    <DynamicIcon name="house-plus" size={25} /> {user.firstName} Listnigs
                  </h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 2xl:grid-cols-3 gap-5">
                    {listings.slice(0, 3).map((listing, idx) => (
                      <ListingCard key={idx} listing={listing} />
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    </section>
  );
};

export default ExploreFacesDetailsPageSection;
