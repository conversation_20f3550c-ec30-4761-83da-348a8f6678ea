"use client";

import React from "react";
import Link from "next/link";
import ListingImagesSlide from "./ListingImagesSlide";
import AddToWishlist from "@/components/Buttons/AddToWishlist/AddToWishlist";
import { <PERSON>, CardContent, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { Badge } from "@/components/ui/badge";
import { PropertyData } from "@/typescript/interfaces";
import ListingCardAction from "./ListingCardAction";
import { usePathname } from "next/navigation";
import { amenitiesLabel, roomTypeLabels } from "@/constants/constants";
import formattedCurrency from "@/lib/currencyFormatter";

interface ListingCardProps {
  listing: PropertyData;
  showListingActions?: boolean;
}

const ListingCard = ({ listing, showListingActions = false }: ListingCardProps) => {
  const pathname = usePathname();
  const {
    fullAddress,
    amenities,
    residenceType,
    availabilityDate,
    monthlyRent,
    photos,
    spaceType,
  } = listing;

  const hideWishlistButton = pathname.includes("my-spaces");

  return (
    <Card className="text-[#555555] gap-[16px] py-[10px] relative">
      <CardHeader className="px-[10px]">
        <ListingImagesSlide images={photos} />
        {/* Add to Wishlist */}
        {!hideWishlistButton && (
          <AddToWishlist
            className="absolute top-5 right-5 z-10 rounded-full cursor-pointer"
            listingId={listing._id as string}
          />
        )}
      </CardHeader>
      <CardContent className="px-[20px] space-y-4">
        <div className="flex justify-between items-center">
          <Link href={`/explore-spaces/${listing._id}`} className="flex-1 min-w-0">
            <CardTitle
              className="flex items-center gap-2 text-base font-medium truncate"
              title={fullAddress}
            >
              <DynamicIcon name="map-pin" size={24} color="#2980B9" />
              <span className="truncate block">{fullAddress}</span>
            </CardTitle>
          </Link>
          {showListingActions && <ListingCardAction listingId={listing._id as string} />}
        </div>
        <ul className="flex flex-wrap items-center gap-3">
          {amenities?.map((amenity: string, idx: number) => {
            const label = amenitiesLabel[amenity];
            return (
              <li key={idx} className="text-[13px]">
                • {label}
              </li>
            );
          })}
        </ul>
        <div className="flex flex-wrap items-center justify-between gap-[12px]">
          <Badge className="px-4 py-2 md:px-2 md:py-1 lg:px-4 lg:py-2 bg-secondary/10 text-secondary text-[11px] font-medium rounded-[10px]">
            {roomTypeLabels[spaceType]}
          </Badge>
          <Badge className="px-4 py-2 md:px-2 md:py-1 lg:px-4 lg:py-2 bg-primary/10 text-secondary text-[11px] font-medium capitalize rounded-[10px]">
            {residenceType}
          </Badge>
          <Badge className="px-4 py-2 md:px-2 md:py-1 lg:px-4 lg:py-2 bg-secondary/10 text-secondary text-[11px] font-medium rounded-[10px]">
            {availabilityDate}
          </Badge>
        </div>
      </CardContent>
      <CardFooter className="px-[20px] justify-between">
        <Link
          href={`/explore-spaces/${listing._id}`}
          className="flex items-center gap-2 hover:underline text-[12px]"
        >
          View More <DynamicIcon name="move-right" size={16} color="#555555" />
        </Link>
        <p>
          <span className="font-semibold text-[17px] text-primary">
            {formattedCurrency.format(monthlyRent)}
          </span>
          <span className="text-[13px]">/month</span>
        </p>
      </CardFooter>
    </Card>
  );
};

export default ListingCard;
