import { z } from "zod";

export const accountDetailsSchema = z.object({
  firstName: z.string().min(2, "First name is required, minimum 2 characters"),
  lastName: z.string().min(2, "Last name is required, minimum 2 characters"),
  phone: z.string().min(10, "Phone number is required, minimum 10 characters"),
});

export const passwordChangeSchema = z
  .object({
    oldPassword: z.string().min(8, "Old password is required, minimum 8 characters"),
    newPassword: z.string().min(8, "New password is required, minimum 8 characters"),
    confirmPassword: z.string().min(8, "Confirm password is required, minimum 8 characters"),
  })
  .refine((data) => data.newPassword === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

export const notificationPreferencesSchema = z.object({
  newListing: z.boolean(),
  newMessage: z.boolean(),
  favoriteListing: z.boolean(),
  finishProfile: z.boolean(),
  finishListing: z.boolean(),
  chatRequest: z.boolean(),
});

export const accountStatusSchema = z.object({
  profile: z.boolean(),
  listing: z.boolean(),
});

export const avatarSchema = z.object({
  avatar: z.object({
    originalName: z.string(),
    fileName: z.string(),
    fileSize: z.number(),
    mimeType: z.string(),
    filePath: z.string(),
  }),
});

export type AccountDetailsData = z.infer<typeof accountDetailsSchema>;
export type PasswordChangeData = z.infer<typeof passwordChangeSchema>;
export type NotificationPreferencesData = z.infer<typeof notificationPreferencesSchema>;
export type AccountStatusData = z.infer<typeof accountStatusSchema>;
export type AvatarData = z.infer<typeof avatarSchema>;
