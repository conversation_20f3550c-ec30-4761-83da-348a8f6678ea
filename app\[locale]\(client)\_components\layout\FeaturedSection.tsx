import React from "react";
import FeaturedCard from "../FeaturedCard/FeaturedCard";
// import { featuredContents } from "@/data";
import { TFeaturedCard } from "@/typescript/types";
import { getTranslations } from "next-intl/server";

const FeaturedSection = async () => {
  const t = await getTranslations("HomePage.FeaturedSection");
  const cards = t.raw("cards");

  return (
    <section className="py-[60px]">
      <div className="container space-y-12">
        {cards.map((featuredContent: TFeaturedCard, idx: number) => (
          <FeaturedCard key={idx} {...featuredContent} />
        ))}
      </div>
    </section>
  );
};

export default FeaturedSection;
