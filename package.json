{"name": "client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "format": "prettier --write ."}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.11", "@radix-ui/react-avatar": "^1.1.10", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.14", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slider": "^1.3.5", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@react-google-maps/api": "^2.20.7", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "input-otp": "^1.4.2", "jwt-decode": "^4.0.0", "lucide-react": "^0.503.0", "next": "15.3.1", "next-intl": "^4.3.4", "react": "^19.0.0", "react-cookie": "^8.0.1", "react-day-picker": "^9.7.0", "react-dom": "^19.0.0", "react-easy-crop": "^5.5.0", "react-flags-select": "^2.5.0", "react-hook-form": "^7.56.4", "react-hot-toast": "^2.5.2", "react-icons": "^5.5.0", "react-share": "^5.2.2", "socket.io-client": "^4.8.1", "swiper": "^11.2.8", "tailwind-merge": "^3.2.0", "use-places-autocomplete": "^4.0.1", "vaul": "^1.1.2", "zod": "^3.25.28", "zustand": "^5.0.5"}, "devDependencies": {"@eslint/eslintrc": "^3", "@tailwindcss/postcss": "^4", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "eslint": "^9", "eslint-config-next": "15.3.1", "prettier": "3.5.3", "tailwindcss": "^4", "tw-animate-css": "^1.2.8", "typescript": "^5"}}