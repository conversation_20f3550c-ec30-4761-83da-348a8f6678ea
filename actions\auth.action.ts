import { LoginFormValues } from "@/lib/validations";
import { AccountDetailsData } from "@/lib/validations/settings";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const handleSignup = async (data: any) => {
  try {
    const response = await fetch(`${baseURL}/web/auth/signup`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const resData = await response.json();
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        message: "An error occurred",
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

// Login
export const handleLogin = async ({ email, password }: LoginFormValues) => {
  try {
    const formData = {
      email,
      password,
    };

    const response = await fetch(`${baseURL}/web/auth/login`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(formData),
    });
    const data = await response.json();
    return {
      ...data,
      status: response.status,
    };
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const handleUpdateAccountDetails = async (data: AccountDetailsData, token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const resData = await response.json();
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        message: "An error occurred while updating account details",
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const handleUpdatePassword = async (
  data: { oldPassword: string; newPassword: string },
  token: string,
) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me/change-password`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const handleUpdateProfilePhoto = async (data: any, token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me/profile-picture`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const handleNotificationPreferences = async (data: any, token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me/notification-preferences`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

export const handleAccountStatus = async (data: any, token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/user/me/status`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(data),
    });

    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        ...resData,
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};
