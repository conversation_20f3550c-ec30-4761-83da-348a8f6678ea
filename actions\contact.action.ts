import toast from "react-hot-toast";
import { ContactUsFormValues, SubscriptionFormValues } from "@/lib/validations";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

// Contact us form
export const handleContactUs = async (data: ContactUsFormValues) => {
  try {
    const response = await fetch(`${baseURL}/web/contact`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    if (response.ok) {
      const resData = await response.json();
      toast.success(resData.message);
    } else {
      return {
        message: "An error occurred",
        status: response.status,
      };
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

// Newsletter subscription
export const handleNewsletter = async (data: SubscriptionFormValues) => {
  try {
    const response = await fetch(`${baseURL}/web/newsletter`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    const resData = await response.json();
    if (response.ok) {
      toast.success(resData.message);
    } else {
      toast.error(resData.message);
    }
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};
