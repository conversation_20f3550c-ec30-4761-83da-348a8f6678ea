import React from "react";
import ExploreFacesDetailsPageSection from "./_components/ExploreFacesDetailsPageSection";
import { getFaceById, getListingByUserId } from "@/actions/faces.action";

interface ExploreFacesDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

const ExploreFacesDetailsPage = async ({ params }: ExploreFacesDetailsPageProps) => {
  const { id } = await params;
  const user = (await getFaceById(id))?.face;
  const listings = (await getListingByUserId(id))?.result?.records || [];

  return <ExploreFacesDetailsPageSection user={user} listings={listings} />;
};

export default ExploreFacesDetailsPage;
