import { useState, useEffect, useCallback } from "react";

interface Coordinates {
  lat: number;
  lng: number;
}

const LOCAL_STORAGE_KEY = "user_location";

const useUserLocation = () => {
  const [location, setLocation] = useState<Coordinates | null>(null);
  const [error, setError] = useState<string | null>(null);

  const getUserLocation = useCallback(() => {
    if (!navigator.geolocation) {
      setError("Geolocation is not supported by your browser");
      return;
    }

    navigator.geolocation.getCurrentPosition(
      (pos) => {
        const { latitude, longitude } = pos.coords;
        setLocation({ lat: latitude, lng: longitude });
        localStorage.setItem(LOCAL_STORAGE_KEY, JSON.stringify({ lat: latitude, lng: longitude }));
        setError(null);
      },
      (err) => {
        setError(err.message);
      },
    );
  }, []);

  useEffect(() => {
    const savedUserLocation = localStorage.getItem(LOCAL_STORAGE_KEY);
    if (savedUserLocation) {
      try {
        const parsed = JSON.parse(savedUserLocation);
        if (parsed.lat && parsed.lng) {
          setLocation(parsed);
          return;
        }
      } catch (e) {
        console.error("Failed to parse saved location", e);
      }
    }
    getUserLocation();
  }, [getUserLocation]);

  return { location, error };
};

export default useUserLocation;
