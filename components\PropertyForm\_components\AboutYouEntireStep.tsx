"use client";

import React from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { AboutYouEntireData, aboutYouEntireSchema } from "@/lib/validations/sign-up-schema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { geneders, sexualOrientations } from "@/constants/constants";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import { UserProfile } from "@/typescript/interfaces";
import { TFormMode } from "@/typescript/types";

interface Args {
  mode: TFormMode;
  user: UserProfile;
}

const AboutYouEntireStep = ({ user, mode }: Args) => {
  console.log("About Entire Place User: ", user);
  const { data, setUserData, currentStep, steps, setCurrentStep } = useSignupStore();
  const form = useForm<AboutYouEntireData>({
    resolver: zodResolver(aboutYouEntireSchema),
    defaultValues: {
      genderIdentity: data.user.genderIdentity || "",
      sexualOrientation: data.user.sexualOrientation || "",
      selfDescription: data.user.selfDescription || "",
    },
  });
  const router = useRouter();

  function onSubmit(values: AboutYouEntireData) {
    const locationURL = mode === "add" ? "/dashboard/my-spaces/add" : "/dashboard/my-spaces/edit";
    setUserData(values);
    router.push(`${locationURL}/${1}`);
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (!form.formState.isValid) return;
    if (currentStep === steps.length - 1) return;
    setCurrentStep(currentStep + 1);
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          name="genderIdentity"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Gender Identity<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select gender identity" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {geneders.map((gender, idx) => (
                    <SelectItem key={idx} value={gender.value}>
                      {gender.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="sexualOrientation"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Sexual Orientation<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select sexual orientation" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {sexualOrientations.map((sexualOrientation, idx) => (
                    <SelectItem key={idx} value={sexualOrientation.value}>
                      {sexualOrientation.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="selfDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel>
                Self Description<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Tell us about yourself..." {...field} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between mt-8">
          <Button type="button" variant="outline" onClick={handlePrevious}>
            Previous
          </Button>
          <Button type="submit" onClick={handleNext} disabled={currentStep === steps.length - 1}>
            {currentStep === steps.length - 1 ? "Complete" : "Next"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default AboutYouEntireStep;
