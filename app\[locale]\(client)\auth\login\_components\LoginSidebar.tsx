"use client";

import React from "react";
import Image from "next/image";
import Logo from "@/components/common/Logo";
import { Card, CardContent } from "@/components/ui/card";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination } from "swiper/modules";

// Import Swiper styles
import "swiper/css";
import "swiper/css/pagination";

const LoginSidebar = () => {
  return (
    <Card className="h-full bg-secondary">
      <CardContent className="flex flex-col justify-between h-full gap-6 px-[30px] xl:px-[58px]">
        <div className="space-y-4 mt-[6rem]">
          <Logo path="/" variant="light" className="w-[160px]!" />
          <h2 className="text-[38px] xl:text-5xl font-bold text-white mt-4 leading-snug">
            Start your journey with us
          </h2>
          <p className="text-white text-base leading-relaxed">
            Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor
            incididunt ut labore et dolore magna aliqua.
          </p>
        </div>
        <ReviewCard />
      </CardContent>
    </Card>
  );
};

const ReviewCard = () => {
  return (
    <Swiper pagination={true} modules={[Pagination]} className="w-full h-auto mt-auto pb-8!">
      <SwiperSlide>
        <div className="bg-[#0063A4] text-white p-5 rounded-md space-y-6">
          <p className="text-white text-base leading-relaxed">
            Their expertise in process optimization helped us cut costs by 30% within six months.
            They&apos;re not just consultants—they&apos;re true partners in growth.
          </p>
          <div className="flex items-center gap-4 mt-4">
            <div>
              <Image
                src={"/images/default/user.png"}
                alt="John Doe"
                width={40}
                height={40}
                className="rounded-full"
              />
            </div>
            <div>
              <h3 className="text-white text-base font-bold">John Doe</h3>
              <p className="text-white text-sm">CEO, ABC Company</p>
            </div>
          </div>
        </div>
      </SwiperSlide>
      <SwiperSlide>
        <div className="bg-[#0063A4] text-white p-5 rounded-md space-y-6">
          <p className="text-white text-base leading-relaxed">
            Their expertise in process optimization helped us cut costs by 30% within six months.
            They&apos;re not just consultants—they&apos;re true partners in growth.
          </p>
          <div className="flex items-center gap-4 mt-4">
            <div>
              <Image
                src={"/images/default/user.png"}
                alt="John Doe"
                width={40}
                height={40}
                className="rounded-full"
              />
            </div>
            <div>
              <h3 className="text-white text-base font-bold">John Doe</h3>
              <p className="text-white text-sm">CEO, ABC Company</p>
            </div>
          </div>
        </div>
      </SwiperSlide>
      <SwiperSlide>
        <div className="bg-[#0063A4] text-white p-5 rounded-md space-y-6">
          <p className="text-white text-base leading-relaxed">
            Their expertise in process optimization helped us cut costs by 30% within six months.
            They&apos;re not just consultants—they&apos;re true partners in growth.
          </p>
          <div className="flex items-center gap-4 mt-4">
            <div>
              <Image
                src={"/images/default/user.png"}
                alt="John Doe"
                width={40}
                height={40}
                className="rounded-full"
              />
            </div>
            <div>
              <h3 className="text-white text-base font-bold">John Doe</h3>
              <p className="text-white text-sm">CEO, ABC Company</p>
            </div>
          </div>
        </div>
      </SwiperSlide>
    </Swiper>
  );
};

export default LoginSidebar;
