"use client";

import React, { useEffect, useState } from "react";
import FileUpload from "@/components/ui/file-upload";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { TFormMode } from "@/typescript/types";
import { PropertyData } from "@/typescript/interfaces";

interface Args {
  mode: TFormMode;
  listing?: PropertyData;
}

const SpacePhotoStep = ({ mode, listing }: Args) => {
  const router = useRouter();
  const { currentStep, steps, setPropertyData, setCurrentStep, markStepCompleted } =
    useSignupStore();
  const [files, setFiles] = useState<File[]>([]);

  const locationURL =
    mode === "add" ? "/dashboard/my-spaces/add" : `/dashboard/my-spaces/edit/${listing?._id}`;

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`${locationURL}/${currentStep - 1}`);
  };

  const handleNext = () => {
    // Save the space photos to the store before navigating
    const photoUrls = files.map((file) => URL.createObjectURL(file));
    setPropertyData({ spacePhotos: photoUrls });

    if (currentStep === steps.length - 1) return;
    markStepCompleted(currentStep);
    setCurrentStep(currentStep + 1);
    router.push(`${locationURL}/${currentStep + 1}`);
  };

  // This is called for single file compatibility
  // const handleFileChange = (file: File) => {
  //   setFiles([file]);
  // };

  // This is called when multiple files are selected
  const handleMultipleFilesChange = async (newFiles: File[]) => {
    const baseURL = process.env.NEXT_PUBLIC_API_URL;

    if (newFiles.length > 10) {
      alert("You can only upload up to 10 files");
      return;
    }

    setFiles(newFiles);

    try {
      const uploadPromises = newFiles.map(async (file) => {
        const formData = new FormData();
        formData.append("file", file);
        formData.append("path", "/space");

        const response = await fetch(`${baseURL}/upload/file`, {
          method: "POST",
          body: formData,
        });

        if (!response.ok) {
          throw new Error(`Failed to upload ${file.name}`);
        }

        const data = await response.json();

        return {
          originalName: data.payload.originalName,
          fileName: data.payload.fileName,
          fileSize: data.payload.fileSize,
          mimeType: data.payload.mimeType,
          filePath: data.payload.filePath,
        };
      });

      const uploadedPhotos = await Promise.all(uploadPromises);

      setPropertyData({
        photos: listing?.photos ? [...listing.photos, ...uploadedPhotos] : uploadedPhotos,
      });
    } catch (error) {
      console.error("Upload error:", error);
      alert("Some files failed to upload. Please try again.");
    }
  };

  useEffect(() => {
    if (listing?.photos) {
      setPropertyData({ photos: listing?.photos });
    }
  }, [listing, setPropertyData]);

  return (
    <div>
      <FileUpload onMultipleFilesChange={handleMultipleFilesChange} multiple={true} maxFiles={10} />
      <div className="flex justify-between gap-6 mt-8">
        <Button
          type="button"
          variant="outline"
          onClick={handlePrevious}
          className="flex-1 !py-6 cursor-pointer border-primary"
        >
          Previous
        </Button>
        <Button
          type="submit"
          onClick={handleNext}
          disabled={currentStep === steps.length - 1}
          className="flex-1 !py-6 cursor-pointer"
        >
          {currentStep === steps.length - 1 ? "Complete" : "Continue"}
        </Button>
      </div>
    </div>
  );
};

export default SpacePhotoStep;
