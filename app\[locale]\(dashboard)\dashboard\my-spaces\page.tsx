import React from "react";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import ListingCard from "@/components/Cards/ListingCard/ListingCard";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { DynamicIcon } from "lucide-react/dynamic";
import { cookies } from "next/headers";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

const getMyListings = async () => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const response = await fetch(`${baseURL}/web/space/me`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    return response.json();
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

const MySpacesPage = async () => {
  const listings = (await getMyListings())?.result?.records || [];
  return (
    <React.Fragment>
      <TopBanner title="My Listings" />
      <section>
        <div className="container">
          <div className="flex justify-between items-center gap-4 mb-4">
            <p className="font-semibold">Total {listings?.length} Listings </p>
            <Button asChild className="rounded-full">
              <Link href={"/dashboard/my-spaces/add/0"}>
                <DynamicIcon name="house-plus" /> Add New Listing
              </Link>
            </Button>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
            {listings.map((property: any) => (
              <ListingCard key={property._id} listing={property} showListingActions={true} />
            ))}
          </div>
        </div>
      </section>
    </React.Fragment>
  );
};

export default MySpacesPage;
