"use client";

import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { Button } from "@/components/ui/button";
import { FaInstagram } from "react-icons/fa";
import { FaXTwitter } from "react-icons/fa6";
import { useRouter } from "next/navigation";
import { handleSocialRedirect } from "@/utils/socialRedirect";
import { AuthStore, authStore } from "@/store/authStore";
import GoogleIcon from "@/icons/GoogleIcon";
import FacebookIcon from "@/icons/FacebookIcon";

const AccountVerification = () => {
  const router = useRouter();
  const { user } = authStore((state: AuthStore) => state);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl text-secondary font-medium flex items-center gap-2.5">
          <DynamicIcon name="user-round-check" size={24} /> Account Verifications
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-5">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-start gap-2">
            <GoogleIcon width={20} height={20} className="mt-2" />
            <div>
              <h5 className="text-lg font-semibold text-[#555555]">Google</h5>
              <p className="text-sm text-[#555555]">
                Connect to log in to gayroom8 with your Google account
              </p>
            </div>
          </div>
          <div>
            <Button
              className="rounded-full"
              onClick={() => handleSocialRedirect(router, "google", "connect")}
              disabled={user?.isGoogleConnected}
            >
              Connect
            </Button>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-start gap-2">
            <FacebookIcon width={20} height={20} className="mt-2" />
            <div>
              <h5 className="text-lg font-semibold text-[#555555]">Facebook</h5>
              <p className="text-sm text-[#555555]">
                Connect to log in to gayroom8 with your Facebook account
              </p>
            </div>
          </div>
          <div>
            <Button
              className="rounded-full"
              onClick={() => handleSocialRedirect(router, "facebook", "connect")}
              disabled={user?.isFacebookConnected}
            >
              Connect
            </Button>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-start gap-2">
            <FaInstagram size={20} className="mt-2" />
            <div>
              <h5 className="text-lg font-semibold text-[#555555]">Instagram</h5>
              <p className="text-sm text-[#555555]">
                Connect to log in to gayroom8 with your Instagram account
              </p>
            </div>
          </div>
          <div>
            <Button
              className="rounded-full"
              onClick={() => console.log("You clicked social button")}
            >
              Connect
            </Button>
          </div>
        </div>
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
          <div className="flex items-start gap-2">
            <FaXTwitter size={20} className="mt-2" />
            <div>
              <h5 className="text-lg font-semibold text-[#555555]">X</h5>
              <p className="text-sm text-[#555555]">
                Connect to log in to gayroom8 with your X account
              </p>
            </div>
          </div>
          <div>
            <Button
              className="rounded-full"
              onClick={() => console.log("You clicked social button")}
            >
              Connect
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
};

export default AccountVerification;
