import React from "react";
import AccountProfileCard from "../../settings/_components/AccountProfileCard";
import { UserProfile } from "@/typescript/interfaces";
import AboutSection from "./AboutSection";
import TheRoomSection from "./TheRoomSection";
import RoommatePreferencesSection from "./RoommatePreferencesSection";
import RentalPreferencesSection from "./RentalPreferencesSection";
import GuestPreferencesSection from "./GuestPreferencesSection";

interface Args {
  user: UserProfile;
}

const MyProfilePageSection = ({ user }: Args) => {
  const isPrivateRoom = user?.spaceType === "private_room";
  const showPersonalDetails = !(user?.intent === "rent" && user?.spaceType === "entire_place");
  const showRentEntirePlaceFields = !(
    user?.intent === "rent" && user?.spaceType === "entire_place"
  );
  const intent = user?.intent;

  return (
    <section className="py-[30px]">
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 lg:col-span-4 xl:col-span-3">
            <AccountProfileCard user={user} />
          </div>
          <div className="col-span-12 lg:col-span-8 xl:col-span-9 space-y-6">
            <AboutSection
              showPersonalDetails={showPersonalDetails}
              user={user}
              showRentEntirePlaceFields={showRentEntirePlaceFields}
            />
            {intent === "find" && isPrivateRoom && <TheRoomSection user={user} />}
            {(intent === "find" && isPrivateRoom) ||
              (intent === "rent" && isPrivateRoom && <RoommatePreferencesSection user={user} />)}
            {intent === "find" && !isPrivateRoom && <RentalPreferencesSection user={user} />}
            {intent === "rent" && !isPrivateRoom && <GuestPreferencesSection user={user} />}
          </div>
        </div>
      </div>
    </section>
  );
};

export default MyProfilePageSection;
