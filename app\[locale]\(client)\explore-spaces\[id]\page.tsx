import React from "react";
import ExploreSpacesDetailsPageSection from "./_components/ExploreSpacesDetailsPageSection";
import { PropertyData } from "@/typescript/interfaces";
import { getListingById } from "@/actions/spaces.action";

interface ExploreSpacesDetailsPageProps {
  params: Promise<{
    id: string;
  }>;
}

const ExploreSpacesDetailsPage = async ({ params }: ExploreSpacesDetailsPageProps) => {
  const { id } = await params;
  const listing = (await getListingById(id))?.space as PropertyData;

  return <ExploreSpacesDetailsPageSection listing={listing} />;
};

export default ExploreSpacesDetailsPage;
