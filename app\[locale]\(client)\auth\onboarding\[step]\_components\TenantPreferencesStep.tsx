"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Button } from "@/components/ui/button";
import { TenantPrefsData, tenantPrefsSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { handleSignup } from "@/actions/auth.action";
import toast from "react-hot-toast";

const TenantPreferencesStep = () => {
  const router = useRouter();
  const {
    currentStep,
    steps,
    setPropertyData,
    setCurrentStep,
    data,
    clear,
    markStepCompleted,
    resetCompletedSteps,
  } = useSignupStore();
  const form = useForm<TenantPrefsData>({
    resolver: zodResolver(tenantPrefsSchema),
    defaultValues: {
      idealTenantDescription: data.property.idealTenantDescription || "",
    },
  });

  async function onSubmit(values: TenantPrefsData) {
    setPropertyData(values);

    const isLastStep = currentStep === steps.length - 1;

    if (isLastStep) {
      const fullData = {
        ...data,
        property: {
          ...data.property,
          ...values,
        },
      };
      const res = await handleSignup(fullData);
      if (res.status >= 200 && res.status < 300) {
        toast.success(res.message);
        markStepCompleted(currentStep);
        setCurrentStep(0);
        resetCompletedSteps();
        clear();
        router.replace("/auth/thank-you");
      } else {
        toast.error(res.message);
        router.replace("/");
      }
    } else {
      markStepCompleted(currentStep);
      setCurrentStep(0);
      router.push(`/auth/onboarding/${currentStep + 1}`);
    }
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)}>
        <FormField
          name="idealTenantDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Please provide a description of your ideal tenant:
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Ideal roommate description" {...field} className="h-40" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Complete" : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default TenantPreferencesStep;
