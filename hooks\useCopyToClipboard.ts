import { useState } from "react";
import toast from "react-hot-toast";

/**
 * useCopyToClipboard hook
 * @returns A tuple [isCopied, copy]
 * - isCopied: boolean state to track copy status
 * - copy: function to copy text to clipboard
 */
export function useCopyToClipboard(): [boolean, (text: string) => Promise<void>] {
  const [isCopied, setIsCopied] = useState(false);

  const copy = async (text: string) => {
    if (!navigator?.clipboard) {
      console.warn("Clipboard API not supported ❌");
      return;
    }

    try {
      await navigator.clipboard.writeText(text);
      toast.success("Copied to clipboard");
      setIsCopied(true);

      setTimeout(() => setIsCopied(false), 1500);
    } catch (err) {
      console.error("Failed to copy!", err);
      toast.error("Failed to copy");
      setIsCopied(false);
    }
  };

  return [isCopied, copy];
}
