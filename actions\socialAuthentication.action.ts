import { SocialAuthData } from "@/typescript/types";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const socialAuthenticationValidation = async (data: SocialAuthData) => {
  try {
    const response = await fetch(`${baseURL}/web/auth/social/validate`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(data),
    });

    const resData = await response.json();
    if (response.ok) {
      return {
        ...resData,
        status: response.status,
      };
    } else {
      return {
        message: "An error occurred while validating social authentication",
        status: response.status,
      };
    }
  } catch (error: any) {
    console.error("Error in socialAuthenticationValidation:", error);
    throw new Error("Validation failed");
  }
};
