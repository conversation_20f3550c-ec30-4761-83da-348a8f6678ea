import React from "react";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { FAQ } from "@/typescript/types";

interface FaqContentsProps {
  faqs: FAQ[];
}

const FaqContents = ({ faqs }: FaqContentsProps) => {
  return (
    <Accordion type="single" collapsible className="space-y-5 text-[#555]" defaultValue="item-0">
      {faqs.length > 0 ? (
        faqs.map(
          (faq: FAQ, idx: number) =>
            faq.isActive && (
              <AccordionItem
                key={idx}
                value={`item-${idx}`}
                className="p-[30px] border rounded-[14px] shadow-sm"
              >
                <AccordionTrigger className="text-base lg:text-[22px] hover:no-underline cursor-pointer items-center">
                  {faq.title}
                </AccordionTrigger>
                <AccordionContent className="text-base lg:text-[22px] text-secondary">
                  {faq.description}
                </AccordionContent>
              </AccordionItem>
            ),
        )
      ) : (
        <p>No FAQs Found</p>
      )}
    </Accordion>
  );
};

export default FaqContents;
