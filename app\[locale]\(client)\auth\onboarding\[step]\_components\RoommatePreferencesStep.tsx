"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import {
  ageRanges,
  preferredGenders,
  preferredSexualOrientations,
  smokingHabits,
} from "@/constants/constants";
import { Textarea } from "@/components/ui/textarea";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Button } from "@/components/ui/button";
import { RoommatePrefsData, roommatePrefsSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { handleSignup } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { MultiSelect } from "@/components/ui/MultiSelect";

const RoommatePreferencesStep = () => {
  const router = useRouter();
  const {
    currentStep,
    steps,
    setPropertyData,
    data,
    setCurrentStep,
    clear,
    setUserData,
    markStepCompleted,
    resetCompletedSteps,
  } = useSignupStore();
  const form = useForm<RoommatePrefsData>({
    resolver: zodResolver(roommatePrefsSchema),
    defaultValues: {
      preferredGenderIdentity: data.property.preferredGenderIdentity || [],
      preferredSexualOrientation: data.property.preferredSexualOrientation || [],
      preferredAgeRange: data.property.preferredAgeRange || [],
      preferredSmokingHabits: data.property.preferredSmokingHabits || [],
      idealRoommateDescription: data.property.idealRoommateDescription || "",
    },
  });

  async function onSubmit(values: RoommatePrefsData) {
    setPropertyData(values);
    setUserData({
      preferredAgeRange: values.preferredAgeRange,
      preferredGenderIdentity: values.preferredGenderIdentity,
      preferredSexualOrientation: values.preferredSexualOrientation,
      preferredSmokingHabits: values.preferredSmokingHabits,
    });
    // const isLastStep = currentStep === steps.length - 1;
    // if (isLastStep) {
    //   router.push("/");
    //   setCurrentStep(0);
    //   clear();
    // } else {
    //   router.push(`/auth/onboarding/${currentStep + 1}`);
    // }
    const fullDataToSend = {
      ...data,
      property: {
        ...data.property,
        ...values,
      },
    };

    const res = await handleSignup(fullDataToSend);
    if (res.status >= 200 && res.status < 300) {
      toast.success(res.message);
      setCurrentStep(0);
      markStepCompleted(currentStep);
      resetCompletedSteps();
      clear();
      router.replace("/auth/thank-you");
    } else {
      toast.error(res.message);
      router.replace("/");
      setCurrentStep(0);
      markStepCompleted(currentStep);
      resetCompletedSteps();
      clear();
    }
    // console.log({ fullDataToSend });
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    // Check schema is valid
    if (!form.formState.isValid) return;
    if (currentStep === steps.length - 1) return;
    markStepCompleted(currentStep);
    setCurrentStep(currentStep + 1);
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="preferredGenderIdentity"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Gender Identity<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={preferredGenders} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredSexualOrientation"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Sexual Orientation<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={preferredSexualOrientations} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredAgeRange"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Age Range<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={ageRanges} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredSmokingHabits"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Smoking Habits<span className="text-red-500">*</span>
              </FormLabel>
              <MultiSelect defaultChecked options={smokingHabits} onValueChange={field.onChange} />
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="idealRoommateDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Describe Your Ideal Roommate<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Ideal roommate description" {...field} className="h-40" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" onClick={handleNext} className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Submit" : "Next"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default RoommatePreferencesStep;
