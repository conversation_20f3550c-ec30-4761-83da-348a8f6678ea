"use client";

import React from "react";
import {
  FacebookShareButton,
  FacebookIcon,
  TwitterShareButton,
  WhatsappShareButton,
  WhatsappIcon,
  XIcon,
  ThreadsShareButton,
  ThreadsIcon,
} from "react-share";

interface SocialShareActionProps {
  url: string;
  title?: string;
}

const SocialShareAction = ({ url = "", title = "gayroom8" }: SocialShareActionProps) => {
  const hashtag = "#gayroom8";
  return (
    <div className="flex items-center gap-4">
      <FacebookShareButton url={url} hashtag={hashtag} title={title}>
        <FacebookIcon size={32} round />
      </FacebookShareButton>
      <ThreadsShareButton url={url} title={title}>
        <ThreadsIcon size={32} round />
      </ThreadsShareButton>
      <TwitterShareButton url={url} title={title}>
        <XIcon size={32} round />
      </TwitterShareButton>
      <WhatsappShareButton url={url} title={title}>
        <WhatsappIcon size={32} round />
      </WhatsappShareButton>
    </div>
  );
};

export default SocialShareAction;
