import React from "react";
import Image from "next/image";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import { handleGetBlogBySlug } from "@/actions/blog.action";
import SocialShareAction from "./_components/SocialShareAction";

interface BlogDetailsPageProps {
  params: Promise<{
    slug: string;
  }>;
}

const BlogDetailsPage = async ({ params }: BlogDetailsPageProps) => {
  const { slug } = await params;
  const blog = (await handleGetBlogBySlug(slug))?.blog;
  const thumbnailURL = process.env.NEXT_PUBLIC_CDN_URL + "/blog/" + blog?.thumbnail;
  const url = process.env.NEXT_PUBLIC_FRONTEND_URL + "/blog/" + blog?.slug;

  return (
    <React.Fragment>
      <TopBanner title="About Blog" />
      <section>
        <div className="container">
          <div className="grid grid-cols-12 gap-4">
            <article className="col-span-12 lg:col-span-9 space-y-5">
              <h2 className="text-2xl lg:text-4xl text-secondary font-bold">{blog?.title}</h2>
              <Image
                src={thumbnailURL}
                alt={blog?.title || "Blog Title"}
                width={800}
                height={800}
                priority
              />

              <div
                className="space-y-5 text-gray-700"
                dangerouslySetInnerHTML={{ __html: blog?.description || "" }}
              />
            </article>
            <div className="col-span-12 lg:col-span-3 space-y-5">
              <div className="space-y-2">
                <h3 className="text-xl font-bold text-secondary">Related Blogs</h3>
                {/* Related Blogs */}
              </div>
              <div className="space-y-2">
                <h3 className="text-xl font-bold text-secondary">Share Socials</h3>
                <SocialShareAction url={url} title={blog?.title} />
              </div>
            </div>
          </div>
        </div>
      </section>
    </React.Fragment>
  );
};

export default BlogDetailsPage;
