"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Switch } from "@/components/ui/switch";
import {
  NotificationPreferencesData,
  notificationPreferencesSchema,
} from "@/lib/validations/settings";
import { handleNotificationPreferences } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { UserProfile } from "@/typescript/interfaces";
import { useCookies } from "react-cookie";

const NotificationPreferencesForm = ({ user }: { user: UserProfile }) => {
  const [cookies] = useCookies(["token"]);
  const form = useForm<NotificationPreferencesData>({
    resolver: zodResolver(notificationPreferencesSchema),
    defaultValues: {
      newListing: user?.notificationPreferences?.newListing || false,
      newMessage: user?.notificationPreferences?.newMessage || false,
      favoriteListing: user?.notificationPreferences?.favoriteListing || false,
      finishProfile: user?.notificationPreferences?.finishProfile || false,
      finishListing: user?.notificationPreferences?.finishListing || false,
      chatRequest: user?.notificationPreferences?.chatRequest || false,
    },
  });

  const handleSwitchChange =
    (name: string, onChange: (value: boolean) => void) => async (value: boolean) => {
      onChange(value);
      const token = cookies.token;
      // console.log("handleSwitchChange token:", token);
      const backendPayload = {
        type: name.replace(/([A-Z])/g, "_$1").toLowerCase(),
        enabled: value,
      };
      // console.log("handleSwitchChange backendPayload:", backendPayload);

      const response = await handleNotificationPreferences(backendPayload, token);
      if (response.status >= 200 && response.status < 300) {
        toast.success(response.message);
      } else {
        toast.error(response.message);
      }
    };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl text-secondary font-medium flex items-center gap-2.5">
          <DynamicIcon name="bell" size={24} /> Notification Preferences
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form className="space-y-5">
            <FormField
              name="newListing"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <FormLabel className="input-label">New listing notifications</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleSwitchChange(field.name, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="newMessage"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <FormLabel className="input-label">New messages</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleSwitchChange(field.name, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="favoriteListing"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <FormLabel className="input-label">Your listing favorites</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleSwitchChange(field.name, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="finishProfile"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <FormLabel className="input-label">Finish profile</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleSwitchChange(field.name, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="finishListing"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <FormLabel className="input-label">Finish listing</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleSwitchChange(field.name, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              name="chatRequest"
              control={form.control}
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between">
                  <FormLabel className="input-label">Notification of chat requests</FormLabel>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={handleSwitchChange(field.name, field.onChange)}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
          </form>
        </Form>
      </CardContent>
    </Card>
  );
};

export default NotificationPreferencesForm;
