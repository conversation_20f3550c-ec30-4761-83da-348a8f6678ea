import React from "react";
import Link from "next/link";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import ContactUsForm from "./_components/ContactUsForm";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { socialLinks } from "@/data";
import { ISocialLink } from "@/typescript/interfaces";
import { getTranslations } from "next-intl/server";

const ContactUsPage = async () => {
  const t = await getTranslations("ContactPage");
  const title = t("title", { defaultValue: "Contact Us" });
  const formSideTitle = t("formSide.title", { defaultValue: "Get in Touch" });
  const formSideDescription = t("formSide.description", {
    defaultValue:
      "Our team is here to assist you 24 hours a day, 7 days a week. Please send us a message and we'll respond ASAP.",
  });
  const contactInfoTitle = t("contactInfo.title", {
    defaultValue: "Reach Out",
  });
  const emailLabel = t("contactInfo.fields.email", {
    defaultValue: "Email Address:",
  });
  const phoneLabel = t("contactInfo.fields.phone", {
    defaultValue: "Phone Number:",
  });
  const socialLabel = t("contactInfo.fields.followUs", {
    defaultValue: "Follow Us:",
  });
  return (
    <>
      <TopBanner title={title} />
      <section className="py-[40px] lg:py-[60px]">
        <div className="container">
          <div className="grid grid-cols-12 gap-5">
            <div className="space-y-8 col-span-12 lg:col-span-8">
              <div className="space-y-4">
                <h2 className="text-3xl lg:text-[32px] font-semibold text-secondary lg:mt-4">
                  {formSideTitle}
                </h2>
                <p className="text-[#555555] text-base font-normal leading-relaxed">
                  {formSideDescription}
                </p>
              </div>
              <ContactUsForm />
            </div>
            <Card className="col-span-12 lg:col-span-4">
              <CardHeader>
                <CardTitle className="text-3xl lg:text-[32px] text-secondary font-semibold">
                  {contactInfoTitle}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-7">
                <div className="space-y-2">
                  <h4 className="text-lg font-medium text-secondary flex items-center gap-1.5">
                    <DynamicIcon name="mail" size={18} /> {emailLabel}:
                  </h4>
                  <Link href="mailto:<EMAIL>" className="text-gray-700">
                    <EMAIL>
                  </Link>
                </div>
                <div className="space-y-2">
                  <h4 className="text-lg font-medium text-secondary flex items-center gap-1.5">
                    <DynamicIcon name="phone" size={18} />
                    {phoneLabel}:
                  </h4>
                  <Link href="tel:(*************" className="text-gray-700">
                    (*************
                  </Link>
                </div>
                <div className="space-y-2">
                  <h4 className="text-lg font-medium text-secondary flex items-center gap-1.5">
                    {socialLabel}:
                  </h4>
                  <ul className="flex items-center gap-[25px]">
                    {socialLinks.map((socialLink: ISocialLink, idx: number) => (
                      <li key={idx}>
                        <Link
                          href={socialLink.href}
                          title={socialLink.label}
                          className="border border-[#555] text-[#555] p-3 rounded-full w-[49px] h-[49px] flex justify-center items-center"
                        >
                          <socialLink.icon size={18} />
                        </Link>
                      </li>
                    ))}
                  </ul>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>
    </>
  );
};

export default ContactUsPage;
