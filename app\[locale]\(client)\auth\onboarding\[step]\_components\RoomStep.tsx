"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { But<PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Input } from "@/components/ui/input";
import { RoomData, roomSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  bathRoom,
  bedroomSize,
  brightness,
  furnished,
  roomFeatures,
  utilitiesIncluded,
} from "@/constants/constants";
import { MultiSelect } from "@/components/ui/MultiSelect";
import { formatNumberWithCommas, isValidNumberInput, removeCommas } from "@/utils/numberFormatter";
import { DynamicIcon } from "lucide-react/dynamic";

const RoomStep = () => {
  const router = useRouter();
  const { currentStep, steps, setPropertyData, data, setCurrentStep, markStepCompleted } =
    useSignupStore();
  const form = useForm<RoomData>({
    resolver: zodResolver(roomSchema),
    defaultValues: {
      availabilityDate: data.property.availabilityDate || "",
      availabilityDuration: data.property.availabilityDuration || 1,
      minimumDuration: data.property.minimumDuration || 1,
      monthlyRent: data.property.monthlyRent || 0,
      depositAmount: data.property.depositAmount || 0,
      leaseRequired: data.property.leaseRequired || "",
      requiredReferences: data.property.requiredReferences || "",
      utilitiesIncluded: data.property.utilitiesIncluded || "",
      furnished: data.property.furnished || "",
      bedroomSize: data.property.bedroomSize || "",
      brightness: data.property.brightness || "",
      bathroom: data.property.bathroom || "",
      roomFeatures: data.property.roomFeatures || [],
    },
  });

  function onSubmit(values: any) {
    setPropertyData(values);
    markStepCompleted(currentStep);
    router.push(`/auth/onboarding/${currentStep + 1}`);
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  const handleNext = () => {
    // Check schema is valid
    if (!form.formState.isValid) return;
    if (currentStep === steps.length - 1) return;
    markStepCompleted(currentStep);
    setCurrentStep(currentStep + 1);
    router.push(`/auth/onboarding/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="availabilityDate"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Availability Date<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Availability Date"
                  type="date"
                  {...field}
                  className="input-field"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="availabilityDuration"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Availability Duration<span className="text-red-500">*</span>
              </FormLabel>
              {field.value <= 11 ? (
                <>
                  <FormControl>
                    <Slider
                      min={1}
                      step={1}
                      max={12}
                      value={[field.value || 1]}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                  </FormControl>
                  <p className="text-sm text-muted-foreground">{field.value || 1} Months</p>
                </>
              ) : (
                <FormControl>
                  <Select
                    defaultValue={field.value === 12 ? "12" : "13"}
                    onValueChange={(value) => field.onChange(parseInt(value))}
                  >
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="11">Less than 1 Year</SelectItem>
                      <SelectItem value="12">1 Year</SelectItem>
                      <SelectItem value="13">1 Year+</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              )}
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="minimumDuration"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Minimum Duration<span className="text-red-500">*</span>
              </FormLabel>
              {field.value <= 11 ? (
                <>
                  <FormControl>
                    <Slider
                      min={1}
                      step={1}
                      max={12}
                      value={[field.value || 1]}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                  </FormControl>
                  <p className="text-sm text-muted-foreground">{field.value || 1} Months</p>
                </>
              ) : (
                <FormControl>
                  <Select
                    defaultValue={field.value === 12 ? "12" : "11"}
                    onValueChange={(value) => field.onChange(parseInt(value))}
                  >
                    <SelectTrigger className="w-full input-field !py-6">
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="11">Less than 1 Year</SelectItem>
                      <SelectItem value="12">1 Year</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              )}
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="monthlyRent"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Monthly Rent<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <DynamicIcon
                    name="dollar-sign"
                    size={16}
                    className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-600"
                  />
                  <Input
                    placeholder="Monthly Rent"
                    {...field}
                    value={formatNumberWithCommas(field.value || "")}
                    onChange={(e) => {
                      const raw = removeCommas(e.target.value);
                      if (isValidNumberInput(raw)) {
                        field.onChange(raw);
                      }
                    }}
                    className="input-field pl-8"
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="depositAmount"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Deposite Amount<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <DynamicIcon
                    name="dollar-sign"
                    size={16}
                    className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-600"
                  />
                  <Input
                    placeholder="Deposite Amount"
                    {...field}
                    value={formatNumberWithCommas(field.value || "")}
                    onChange={(e) => {
                      const raw = removeCommas(e.target.value);
                      if (isValidNumberInput(raw)) {
                        field.onChange(raw);
                      }
                    }}
                    className="input-field pl-8"
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="leaseRequired"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Lease Required<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="y">Yes</SelectItem>
                  <SelectItem value="n">No</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="requiredReferences"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Required References<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  <SelectItem value="n">None</SelectItem>
                  <SelectItem value="1">1</SelectItem>
                  <SelectItem value="2">2</SelectItem>
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="utilitiesIncluded"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Utilities Included<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {utilitiesIncluded.map((item) => (
                    <SelectItem key={item.value} value={item.value}>
                      {item.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="furnished"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Furnished<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {furnished.map((item) => (
                    <SelectItem key={item.value} value={item.value}>
                      {item.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="bedroomSize"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Bedroom Size<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bedroomSize.map((bedRoom) => (
                    <SelectItem key={bedRoom.value} value={bedRoom.value}>
                      {bedRoom.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="brightness"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Brightness<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {brightness.map((brightness) => (
                    <SelectItem key={brightness.value} value={brightness.value}>
                      {brightness.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="bathroom"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Bathroom<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bathRoom.map((bathRoom) => (
                    <SelectItem key={bathRoom.value} value={bathRoom.value}>
                      {bathRoom.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />

        <FormField
          name="roomFeatures"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Room Features<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={roomFeatures}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />

        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button
            type="submit"
            onClick={handleNext}
            disabled={currentStep === steps.length - 1}
            className="flex-1 !py-6 cursor-pointer"
          >
            {currentStep === steps.length - 1 ? "Complete" : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default RoomStep;
