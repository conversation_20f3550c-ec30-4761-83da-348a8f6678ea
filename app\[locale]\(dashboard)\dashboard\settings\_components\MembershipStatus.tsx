import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { DynamicIcon } from "lucide-react/dynamic";
import { Badge } from "@/components/ui/badge";
import MembershipStatusAction from "./MembershipStatusAction";
import { UserProfile } from "@/typescript/interfaces";
import { format } from "date-fns";

interface Args {
  user: UserProfile;
}

const MembershipStatus = ({ user }: Args) => {
  const membership = user?.membership;

  const modifyDate = (date: string) => {
    return format(new Date(date), "dd/MM/yyyy");
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl text-secondary font-medium flex items-center gap-2.5">
          <DynamicIcon name="id-card" size={24} /> Membership Status
        </CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="text-primary text-center">Plan</TableHead>
              <TableHead className="text-primary text-center">Start Date</TableHead>
              <TableHead className="text-primary text-center">End Date</TableHead>
              <TableHead className="text-primary text-center">Amount</TableHead>
              <TableHead className="text-primary text-center">Status</TableHead>
              <TableHead className="text-primary text-center">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {membership ? (
              <TableRow>
                <TableCell className="text-center">
                  {membership?.membershipId.duration} Days
                </TableCell>
                <TableCell className="text-center">{modifyDate(membership?.startDate)}</TableCell>
                <TableCell className="text-center">{modifyDate(membership?.endDate)}</TableCell>
                <TableCell className="text-center">${membership?.membershipId.price}</TableCell>
                <TableCell className="text-center">
                  <Badge className="bg-green-600/20 text-green-600 rounded-full capitalize">
                    {membership?.status}
                  </Badge>
                </TableCell>
                <TableCell className="text-center">
                  <MembershipStatusAction />
                </TableCell>
              </TableRow>
            ) : (
              <TableRow>
                <TableCell colSpan={6}>No membership found</TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default MembershipStatus;
