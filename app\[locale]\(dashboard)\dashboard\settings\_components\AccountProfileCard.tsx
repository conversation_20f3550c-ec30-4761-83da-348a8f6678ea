"use client";

import React, { useState } from "react";
import Link from "next/link";
import Image from "next/image";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, CardContent, CardFooter, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { useForm } from "react-hook-form";
import { Form, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { AvatarData, avatarSchema } from "@/lib/validations/settings";
import { DFEAULT_USER } from "@/constants/constants";
import { UserProfile } from "@/typescript/interfaces";
import { handleUpdateProfilePhoto } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { AuthStore, authStore } from "@/store/authStore";
import { preferredGenderIdentity<PERSON>abels, userIntentLabel } from "@/constants/listing.constants";
import { useCookies } from "react-cookie";

interface AccountProfileCardProps {
  user: UserProfile;
}

const AccountProfileCard = ({ user }: AccountProfileCardProps) => {
  const [cookies] = useCookies(["token"]);
  const { user: userData } = authStore((state: AuthStore) => state);
  const router = useRouter();
  const form = useForm<AvatarData>({
    resolver: zodResolver(avatarSchema),
    defaultValues: {
      avatar: {
        originalName: "",
        fileName: "",
        fileSize: 0,
        mimeType: "",
        filePath: "",
      },
    },
  });

  const profileUrl = userData?.avatar?.filePath
    ? process.env.NEXT_PUBLIC_CDN_URL + "/" + user?.avatar?.filePath + "/" + user?.avatar?.fileName
    : "";

  const [previewUrl, setPreviewUrl] = useState(profileUrl); // 👈 preview state

  const handleAvatarUpload = async (file: File) => {
    const token = cookies.token;
    const baseURL = process.env.NEXT_PUBLIC_API_URL;
    try {
      const formData = new FormData();
      formData.append("file", file);
      formData.append("path", "/user/profile");
      const response = await fetch(`${baseURL}/upload/file`, {
        method: "POST",
        body: formData,
      });

      if (!response.ok) {
        throw new Error(`Failed to upload ${file.name}`);
      }

      const data = (await response.json())?.payload;

      const avatarData = {
        originalName: data.originalName,
        fileName: data.fileName,
        fileSize: data.fileSize.toString(),
        mimeType: data.mimeType,
        filePath: data.filePath,
      };

      const updateResponse = await handleUpdateProfilePhoto(avatarData, token);

      if (updateResponse.status >= 200 && updateResponse.status < 300) {
        toast.success(updateResponse.message);
        router.refresh();
      } else {
        toast.error(updateResponse.message);
        return;
      }
    } catch (error) {
      console.error("❌ Upload failed:", error);
    }
  };

  return (
    <Form {...form}>
      <form>
        <Card className="py-[10px]">
          <CardContent className="text-center space-y-2 px-[10px]">
            <div className="flex justify-end items-center mb-4">
              <Badge className="bg-secondary rounded-full">
                {user?.isAccountVerified ? "Account Verified" : "Account Not Verified"}
              </Badge>
            </div>
            <div className="relative w-40 h-40 mx-auto">
              <Image
                src={previewUrl || profileUrl || DFEAULT_USER}
                alt={(user?.firstName as string) || DFEAULT_USER}
                width={150}
                height={150}
                priority
                className="w-[150px] h-[150px] object-cover rounded-full mx-auto outline-2 outline-secondary outline-offset-8"
              />
              <span className="absolute bottom-0 right-5 w-6 h-6 p-1 bg-secondary text-white inline-flex items-center justify-center rounded-full">
                <FormField
                  name="avatar"
                  control={form.control}
                  render={({}) => (
                    <FormItem>
                      <FormLabel htmlFor="avatar">
                        <DynamicIcon name="camera" size={15} />
                      </FormLabel>
                      <Input
                        id="avatar"
                        type="file"
                        accept="image/*"
                        hidden
                        onChange={(e) => {
                          const file = e.target.files?.[0];
                          if (file) {
                            handleAvatarUpload(file);
                            setPreviewUrl(URL.createObjectURL(file));
                          }
                        }}
                      />
                    </FormItem>
                  )}
                />
              </span>
            </div>
            <Link href={`/explore-faces/${user?._id}`}>
              <CardTitle className="text-[27px] font-medium text-[#555555]">
                {user?.firstName || "N/A"}
              </CardTitle>
            </Link>
            <p className="text-lg text-secondary">{userIntentLabel[user?.intent ?? ""] || "N/A"}</p>
          </CardContent>
          <CardFooter className="border-t-2 border-dashed flex-col gap-5 px-[10px]">
            <div className="flex justify-center w-full mt-4">
              <p className="flex items-center gap-2 text-base text-[#555555]">
                <span className="w-9 h-9 p-2 bg-secondary text-white inline-flex items-center justify-center rounded-full">
                  <DynamicIcon name="venus-and-mars" size={25} />
                </span>
                {preferredGenderIdentityLabels[user?.genderIdentity ?? ""] || "N/A"}
              </p>
            </div>
          </CardFooter>
        </Card>
      </form>
    </Form>
  );
};

export default AccountProfileCard;
