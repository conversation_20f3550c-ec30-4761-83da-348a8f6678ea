import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import MessageSearchbar from "./MessageSearchbar";
import MessageList from "./MessageList";
import { Conversation } from "@/typescript/interfaces";

interface Args {
  conversations: Conversation[];
}

const MessageSidebar = ({ conversations }: Args) => {
  // console.log("Conversations:", conversations);
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl font-bold text-secondary">Messages</CardTitle>
        <MessageSearchbar />
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[500px]">
          <div className="space-y-3">
            {conversations.map((conversation: Conversation) => (
              <MessageList key={conversation._id} conversation={conversation} />
            ))}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
};

export default MessageSidebar;
