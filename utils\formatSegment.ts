/**
 * Format a URL segment into a readable display name
 *
 * @param {string} segment - The URL segment
 * @returns {string} Formatted segment name
 */
function formatSegment(segment: string) {
  let result = segment.replace(/[-_]/g, " ");

  result = result
    .split(" ")
    .map((word) => word.charAt(0).toUpperCase() + word.slice(1))
    .join(" ");

  if (/^\d+$/.test(segment)) {
    return `Item ${segment}`;
  }

  return result;
}
export default formatSegment;
