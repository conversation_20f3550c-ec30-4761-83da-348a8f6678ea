"use client";

import React from "react";
import toast from "react-hot-toast";
import { Button } from "@/components/ui/button";
import { handleEmailVerify } from "@/actions/verify.action";
import { useRouter } from "next/navigation";

interface VerifyActiveButtonProps {
  token: string;
}

const VerifyActiveButton = ({ token }: VerifyActiveButtonProps) => {
  const router = useRouter();
  const handleVerify = async () => {
    const verifyResponse = await handleEmailVerify(token);
    if (verifyResponse.status >= 200 && verifyResponse.status < 300) {
      toast.success(verifyResponse.message);
      router.push("/");
    } else {
      toast.error(verifyResponse.message);
      router.push("/");
    }
  };

  return (
    <Button className="w-full h-auto rounded-full cursor-pointer" onClick={handleVerify}>
      Verify Your Account
    </Button>
  );
};

export default VerifyActiveButton;
