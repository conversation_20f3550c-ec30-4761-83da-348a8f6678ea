import { cookies } from "next/headers";
import { NextRequest, NextResponse } from "next/server";
import { PUBLIC_PATHS } from "./constants/constants";
import { routing } from "./i18n/routing";
import createMiddleware from "next-intl/middleware";

const intlMiddleware = createMiddleware(routing);

export async function middleware(request: NextRequest) {
  const response = intlMiddleware(request);

  const baseUrl = process.env.NEXT_PUBLIC_API_URL;
  const cookieStore = await cookies();
  const { pathname } = request.nextUrl;
  const token = cookieStore.get("token")?.value;
  const isPublicPath = PUBLIC_PATHS.some((path) => pathname.startsWith(path));

  if (!token && isPublicPath) {
    return response;
  }

  try {
    const userResponse = await fetch(`${baseUrl}/web/user/me`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      cache: "no-store",
      credentials: "include",
    });

    const user = await userResponse.json();

    if (user?.user && isPublicPath) {
      return NextResponse.redirect(new URL("/dashboard/settings", request.url));
    }
  } catch (error) {
    console.error("⚠️ Middleware Error:", error);
    if (!isPublicPath) {
      return NextResponse.redirect(new URL("/", request.url));
    }
  }

  return response;
}

export const config = {
  matcher: ["/((?!api|trpc|_next|_vercel|.*\\..*).*)", "/dashboard/:path*"],
};
