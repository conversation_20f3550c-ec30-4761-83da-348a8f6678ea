"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import { UserProfile } from "@/typescript/interfaces";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { aboutYouSchema, AboutYouData } from "@/lib/validations/profileForm.schema";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  cleanliness,
  describeMyselfAs,
  fluentLanguages,
  geneders,
  sexualOrientations,
  smokeCigarettes,
  smokeMarijuana,
  travels,
  workFromHome,
  zodiacs,
} from "@/constants/constants";
import { MultiSelect } from "@/components/ui/MultiSelect";
import { Textarea } from "@/components/ui/textarea";
import toast from "react-hot-toast";
import { updateUserProfile } from "@/actions/user.action";
import { useRouter } from "next/navigation";
import { useCookies } from "react-cookie";
import { Slider } from "@/components/ui/slider";

interface Args {
  isPrivateRoom?: boolean;
  user: UserProfile;
  showPersonalDetails?: boolean;
  isEdit: boolean;
  setIsEdit: (value: boolean) => void;
  showRentEntirePlaceFields?: boolean;
}

const AboutForm = ({ isEdit, setIsEdit, user, showRentEntirePlaceFields }: Args) => {
  const [cookies] = useCookies(["token"]);
  const router = useRouter();
  const form = useForm<AboutYouData>({
    resolver: zodResolver(aboutYouSchema),
    defaultValues: {
      genderIdentity: user?.genderIdentity || "",
      sexualOrientation: user?.sexualOrientation || "",
      age: user?.age || 0,
      smokeCigarettes: user?.smokeCigarettes || "",
      smokeMarijuana: user?.smokeMarijuana || "",
      workFromHome: user?.workFromHome || "",
      travel: user?.travel || "",
      cleanliness: user?.cleanliness || "",
      fluentLanguages: user?.fluentLanguages || [],
      describeMyselfAs: user?.describeMyselfAs || [],
      zodiac: user?.zodiac || "",
      selfDescription: user?.selfDescription || "",
    },
  });

  const token = cookies.token;

  const onSubmit = async (data: AboutYouData) => {
    const response = await updateUserProfile(data, token);
    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message);
      setIsEdit(!isEdit);
      router.refresh();
    } else {
      toast.error(response.message);
      setIsEdit(!isEdit);
      router.refresh();
    }
  };

  return (
    <div className="space-y-7 bg-white shadow p-4 rounded-md border">
      <div className="space-y-3 relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="user" size={24} /> About {user.firstName || "N/A"}
        </h2>
      </div>
      <Form {...form}>
        <form className="space-y-5" onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            name="genderIdentity"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">
                  Gender Identity <span className="text-red-500">*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {geneders.map((gender, idx) => (
                      <SelectItem key={idx} value={gender.value}>
                        {gender.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          <FormField
            name="sexualOrientation"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">
                  Sexual Orientation <span className="text-red-500">*</span>
                </FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {sexualOrientations.map((sexualOrientation, idx) => (
                      <SelectItem key={idx} value={sexualOrientation.value}>
                        {sexualOrientation.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
                <FormMessage />
              </FormItem>
            )}
          />
          {showRentEntirePlaceFields && (
            <>
              <FormField
                name="age"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Age <span className="inline md:hidden">: {field.value}</span>{" "}
                      <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl className="mt-2">
                      <Slider
                        min={18}
                        step={1}
                        max={100}
                        value={[field.value || 18]}
                        onValueChange={(value) => field.onChange(value[0])}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="smokeCigarettes"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Smoke Cigarettes <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full rounded-full py-6! pl-6">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {smokeCigarettes.map((cigarettes, idx) => (
                          <SelectItem key={idx} value={cigarettes.value}>
                            {cigarettes.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="smokeMarijuana"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Smoke Marijuana <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full rounded-full py-6! pl-6">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {smokeMarijuana.map((marijuana, idx) => (
                          <SelectItem key={idx} value={marijuana.value}>
                            {marijuana.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="workFromHome"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Work From Home <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full rounded-full py-6! pl-6">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {workFromHome.map((work, idx) => (
                          <SelectItem key={idx} value={work.value}>
                            {work.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="travel"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Travel <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full rounded-full py-6! pl-6">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {travels.map((travel, idx) => (
                          <SelectItem key={idx} value={travel.value}>
                            {travel.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="cleanliness"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Cleanliness <span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full rounded-full py-6! pl-6">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {cleanliness.map((cleanlines, idx) => (
                          <SelectItem key={idx} value={cleanlines.value}>
                            {cleanlines.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="fluentLanguages"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Fluent Languages <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={fluentLanguages}
                        onValueChange={field.onChange}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="describeMyselfAs"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      I describe myself as <span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <MultiSelect
                        options={describeMyselfAs}
                        onValueChange={field.onChange}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                name="zodiac"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">Zodiac</FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full rounded-full py-6! pl-6">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {zodiacs.map((zodiac, idx) => (
                          <SelectItem key={idx} value={zodiac.value}>
                            {zodiac.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </>
          )}
          <FormField
            name="selfDescription"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">
                  Self Description <span className="text-red-500">*</span>
                </FormLabel>
                <FormControl>
                  <Textarea {...field} placeholder="Tell us about yourself..." />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="w-full max-w-sm mx-auto ">
            <Button type="submit" className="w-full h-auto py-3 rounded-full cursor-pointer">
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default AboutForm;
