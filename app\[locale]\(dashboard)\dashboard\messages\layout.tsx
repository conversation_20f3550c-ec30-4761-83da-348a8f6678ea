import React from "react";
import MessageSidebar from "./_components/MessageSidebar";
import { cookies } from "next/headers";

const baseURL = process.env.NEXT_PUBLIC_API_URL;

interface Args {
  children: React.ReactNode;
}

const getConversations = async () => {
  const cookieStore = await cookies();
  const token = cookieStore.get("token")?.value;
  try {
    const response = await fetch(`${baseURL}/web/conversation`, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
    });
    return response.json();
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};

const MessagesLayout = async ({ children }: Args) => {
  const { conversations } = await getConversations();

  return (
    <section>
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 md:col-span-5 lg:col-span-4">
            <MessageSidebar conversations={conversations} />
          </div>
          <div className="col-span-12 md:col-span-7 lg:col-span-8">{children}</div>
        </div>
      </div>
    </section>
  );
};

export default MessagesLayout;
