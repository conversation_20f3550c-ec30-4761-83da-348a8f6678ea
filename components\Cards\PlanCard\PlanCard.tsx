import React from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { Plan } from "@/typescript/types";
import { DynamicIcon } from "lucide-react/dynamic";
import PlanCardAction from "./PlanCardAction";

interface PlanCardProps {
  plan: Plan;
  index: number;
}

const colorVariants = [
  {
    text: "text-secondary",
    bg: "bg-primary",
    strokeColor: "#8E44AD",
  },
  {
    text: "text-primary",
    bg: "bg-secondary",
    strokeColor: "#1C7BBA",
  },
];

const PlanCard = ({ plan, index }: PlanCardProps) => {
  const color = index % 2 === 0 ? colorVariants[0] : colorVariants[1];

  return (
    <Card className="shadow-md">
      <CardHeader>
        <CardTitle className={`text-center text-[35px] font-bold ${color.text}`}>
          {plan.name}
        </CardTitle>
        <div
          className={`h-1.5 w-40 mx-auto mt-2 rounded-t-[100%] rounded-b-[100%] ${color.text} bg-current`}
        />
      </CardHeader>
      <CardContent>
        <p className="text-[41px] font-semibold text-[#555555] my-2">
          ${plan.price} <span className="text-sm font-normal">/ for {plan.duration} days</span>
        </p>
        <div>
          <p className="text-[16px] font-medium text-[#555555]">Features:</p>
          <ul className="text-gray-600 mt-4 space-y-[16px]">
            <li className="flex items-center gap-2 text-[18px]">
              <span className="inline-flex justify-center items-center">
                <DynamicIcon name="circle-check" size={18} color={"#8E44AD"} />
              </span>{" "}
              Full Access
            </li>
            <li className="flex items-center gap-2 text-[18px]">
              <span className="inline-flex justify-center items-center">
                <DynamicIcon name="circle-check" size={18} color={"#2980B9"} />
              </span>{" "}
              Unlimited Messaging
            </li>
            <li className="flex items-center gap-2 text-[18px]">
              <span className="inline-flex justify-center items-center">
                <DynamicIcon name="circle-check" size={18} color={"#8E44AD"} />
              </span>{" "}
              Membership Auto-Renews
            </li>
            <li className="flex items-center gap-2 text-[18px]">
              <span className="inline-flex justify-center items-center">
                <DynamicIcon name="circle-check" size={18} color={"#2980B9"} />
              </span>{" "}
              Cancel Anytime
            </li>
          </ul>
        </div>
      </CardContent>
      <CardFooter>
        <PlanCardAction plan={plan} color={color} />
      </CardFooter>
    </Card>
  );
};

export default PlanCard;
