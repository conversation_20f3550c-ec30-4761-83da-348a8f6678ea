"use client";

import React, { useEffect } from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { getSteps } from "@/lib/getOnboardingSteps";
import { useRouter, usePathname } from "next/navigation";
import Link from "next/link";
import { DynamicIcon } from "lucide-react/dynamic";
import clsx from "clsx";

interface OnboardingLayoutProps {
  children: React.ReactNode;
}

const OnboardingLayout = ({ children }: OnboardingLayoutProps) => {
  const router = useRouter();
  const pathname = usePathname();
  const { data, steps, setSteps, currentStep, completedSteps } = useSignupStore();

  // useEffect(() => {
  //   if (!data.user.intent || !data.user.spaceType) {
  //     router.push("/auth/register");
  //   }
  // }, [data.user.intent, data.user.spaceType, router]);

  useEffect(() => {
    if (!steps.length && data.user.intent && data.user.spaceType) {
      const generatedSteps = getSteps(data.user.intent, data.user.spaceType);
      setSteps(generatedSteps);
    }
  }, [data.user.intent, data.user.spaceType, steps.length, setSteps]);

  useEffect(() => {
    if (typeof currentStep === "number" && currentStep < 0) {
      router.replace("/auth/register");
    }
  }, [currentStep, router]);

  // Redirect to current step if user visits the base onboarding path
  useEffect(() => {
    if (pathname === "/auth/onboarding" && steps.length > 0 && currentStep >= 0) {
      router.push(`/auth/onboarding/${currentStep}`);
    }
  }, [pathname, steps.length, currentStep, router]);

  return (
    <section className="pb-[60px]">
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="hidden md:block md:col-span-5 lg:col-span-4">
            <aside className="w-full border-r p-6 pl-[36px] py-[50px] space-y-10 bg-white border rounded-xl">
              {steps.map((step, index) => {
                const isActive = index === currentStep;
                const isCompleted = completedSteps.includes(index);
                const isFirst = index === 0;
                const isLast = index === steps.length - 1;

                return (
                  <Link
                    href={`/auth/onboarding/${index}`}
                    key={index}
                    className="flex items-center gap-3 relative"
                  >
                    {/* Connector Lines */}
                    {!isFirst && (
                      <div
                        className={clsx(
                          "absolute left-[22px] top-[-40px] h-10 w-px",
                          isCompleted ? "bg-primary" : "bg-gray-200",
                        )}
                      />
                    )}
                    {!isLast && (
                      <div
                        className={clsx(
                          "absolute left-[22px] bottom-[-40px] h-10 w-px",
                          isCompleted ? "bg-primary" : "bg-gray-200",
                        )}
                      />
                    )}

                    {/* Step Circle */}
                    <div
                      className={clsx(
                        "relative z-10 flex items-center justify-center w-[44px] h-[44px] rounded-full border-2 text-gray-400",
                        isActive
                          ? "bg-gray-200 border-gray-200 text-[#B4B4B4]"
                          : isCompleted
                            ? "bg-primary border-primary text-white"
                            : "bg-gray-200 text-[#B4B4B4] border-gray-200",
                      )}
                    >
                      <DynamicIcon name={step.icon} size={24} />
                    </div>

                    {/* Label */}
                    <span
                      className={clsx(
                        "text-sm lg:text-base",
                        isActive
                          ? "text-[#555555] font-normal"
                          : isCompleted
                            ? "text-primary"
                            : "text-gray-500",
                      )}
                    >
                      {step.label}
                    </span>
                  </Link>
                );
              })}
            </aside>
          </div>
          <div className="col-span-12 md:col-span-7 lg:col-span-8">{children}</div>
        </div>
      </div>
    </section>
  );
};

export default OnboardingLayout;
