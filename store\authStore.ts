import { create } from "zustand";
import { UserProfile } from "@/typescript/interfaces";

export type AuthStore = {
  user: UserProfile | null;
  isAuthenticated: boolean;
  setUser: (user: UserProfile) => void;
  setIsAuthenticated: (status: boolean) => void;
};

export const authStore = create<AuthStore>((set) => ({
  user: null,
  isAuthenticated: false,
  setUser: (user: UserProfile) => set({ user }),
  setIsAuthenticated: (status: boolean) => set({ isAuthenticated: status }),
}));
