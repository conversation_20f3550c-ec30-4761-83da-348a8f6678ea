import { StepItem } from "@/typescript/interfaces";

// lib/getOnboardingSteps.ts
export function getSteps(intent: string, spaceType: string): StepItem[] {
  if (intent === "rent" && spaceType === "private_room") {
    return [
      { label: "About You", icon: "user-round" },
      { label: "Upload Profile Photo", icon: "image-plus" },
      { label: "The Home", icon: "house-plus" },
      { label: "The Room", icon: "house-plug" },
      { label: "Upload Space Photos", icon: "image-plus" },
      { label: "Roommate Preferences", icon: "user-round-search" },
    ];
  }

  if (intent === "rent" && spaceType === "entire_place") {
    return [
      { label: "About You", icon: "user-round" },
      { label: "Upload Profile Photo", icon: "image-plus" },
      { label: "The Space", icon: "house-plus" },
      { label: "Upload Space Photos", icon: "image-plus" },
      { label: "Rental Basics", icon: "user-round-search" },
      { label: "Tenant Preferences", icon: "bolt" },
    ];
  }

  if (intent === "find" && spaceType === "private_room") {
    return [
      { label: "About You", icon: "user-round" },
      { label: "Upload Profile Photo", icon: "image-plus" },
      { label: "The Room", icon: "house-plug" },
      { label: "Roommate Preferences", icon: "user-round-search" },
    ];
  }

  if (intent === "find" && spaceType === "entire_place") {
    return [
      { label: "About You", icon: "user-round" },
      { label: "Upload Profile Photo", icon: "image-plus" },
      { label: "Rental Preferences", icon: "sliders-horizontal" },
    ];
  }

  return [];
}

export function getListingSteps(intent: string, spaceType: string): StepItem[] {
  if (intent === "rent" && spaceType === "private_room") {
    return [
      { label: "The Home", icon: "house-plus" },
      { label: "The Room", icon: "house-plug" },
      { label: "Upload Space Photos", icon: "image-plus" },
      { label: "Roommate Preferences", icon: "user-round-search" },
    ];
  }

  if (intent === "rent" && spaceType === "entire_place") {
    return [
      { label: "The Space", icon: "house-plus" },
      { label: "Upload Space Photos", icon: "image-plus" },
      { label: "Rental Basics", icon: "user-round-search" },
      { label: "Tenant Preferences", icon: "bolt" },
    ];
  }

  if (intent === "find" && spaceType === "private_room") {
    return [
      { label: "The Room", icon: "house-plug" },
      { label: "Roommate Preferences", icon: "user-round-search" },
    ];
  }

  if (intent === "find" && spaceType === "entire_place") {
    return [{ label: "Rental Preferences", icon: "sliders-horizontal" }];
  }

  return [];
}
