"use client";

import React from "react";
import Link from "next/link";
import { Swiper, SwiperSlide } from "swiper/react";
import { Pagination, Autoplay } from "swiper/modules";
// import { heroContents } from "@/data";
import { Button } from "@/components/ui/button";
import { THeroContent } from "@/typescript/types";
import "swiper/css/pagination";
import QuickSearchBar from "@/components/QuickSearchBar/QuickSearchBar";
import { useTranslations } from "next-intl";

const HeroSection = () => {
  const t = useTranslations("HomePage.HeroSection");
  const slides = t.raw("slides");

  return (
    <section className={"w-full h-[700px] lg:h-dvh py-0 relative"}>
      <Swiper
        loop={true}
        pagination={{
          dynamicBullets: true,
        }}
        // autoplay={{
        //   delay: 5000,
        //   disableOnInteraction: false,
        // }}
        modules={[Pagination, Autoplay]}
      >
        {slides.map((content: THeroContent, idx: number) => (
          <SwiperSlide
            key={idx}
            className="h-full! relative"
            style={{
              backgroundImage: `url(${content.image})`,
              backgroundSize: "cover",
              backgroundPosition: "center",
              backgroundRepeat: "no-repeat",
            }}
          >
            <div className="absolute inset-0 bg-black/5" />
            <div className="container z-10 relative">
              <div className="flex flex-col gap-4 h-full justify-center mt-6 md:mt-12 w-[320px] sm:w-[360px] lg:w-[438px]">
                <h2 className="w-full text-[38px] md:text-[43px] lg:text-[52px] font-semibold text-white">
                  {content.title}
                </h2>
                <Button
                  asChild
                  className="w-full lg:w-[420px] block rounded-full text-center text-xs lg:text-base font-medium h-[35px] lg:h-[40px]"
                >
                  <Link href={"#"}>{content.description}</Link>
                </Button>
              </div>
            </div>
          </SwiperSlide>
        ))}
      </Swiper>
      <QuickSearchBar className="absolute left-0 right-0 bottom-52 sm:bottom-40 lg:bottom-60 z-20 max-w-sm md:max-w-2xl lg:max-w-4xl mx-auto" />
    </section>
  );
};

export default HeroSection;
