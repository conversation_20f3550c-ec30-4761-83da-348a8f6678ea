"use client";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import {
  Form,
  FormField,
  FormItem,
  FormLabel,
  FormControl,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectTrigger,
  SelectValue,
  SelectContent,
  SelectItem,
} from "@/components/ui/select";
import { Button } from "@/components/ui/button";
import { registerSchema } from "@/lib/validations/sign-up-schema";
import { useSignupStore } from "@/store/userSignUpStore";
import { getSteps } from "@/lib/getOnboardingSteps";
import { useEffect } from "react";
import { spaceTypes } from "@/constants/constants";
import { Card, CardContent } from "@/components/ui/card";
import GoogleIcon from "@/icons/GoogleIcon";
import { FaInstagram } from "react-icons/fa";
import FacebookIcon from "@/icons/FacebookIcon";
import { FaXTwitter } from "react-icons/fa6";
import Logo from "@/components/common/Logo";
import Link from "next/link";
import { handleSocialRedirect } from "@/utils/socialRedirect";

export default function RegisterForm() {
  const router = useRouter();
  const { setUserData, setSteps, data, setCurrentStep } = useSignupStore();

  const form = useForm<z.infer<typeof registerSchema>>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      firstName: "",
      lastName: "",
      email: "",
      intent: "",
      spaceType: "",
    },
    mode: "onSubmit",
  });

  // Load persisted data into form when component mounts
  useEffect(() => {
    if (data.user) {
      form.reset({
        firstName: data.user.firstName || "",
        lastName: data.user.lastName || "",
        email: data.user.email || "",
        intent: data.user.intent || "",
        spaceType: data.user.spaceType || "",
      });
    }
  }, [data.user, form]);

  // Redirect to onboarding if user is already registered
  // useEffect(() => {
  //   if (data.user.intent && data.user.spaceType) {
  //     router.push(`/auth/onboarding/${currentStep}`);
  //   }
  // }, [data.user.intent, data.user.spaceType, currentStep, router]);

  function onSubmit(values: z.infer<typeof registerSchema>) {
    // console.log("Register form data:", values);
    // if (!form.formState.isValid) return;
    setUserData(values);
    const steps = getSteps(values.intent, values.spaceType);
    setSteps(steps);
    setCurrentStep(0);
    router.push(`/auth/onboarding/${0}`);
  }

  return (
    <Card className="py-[48px]">
      <CardContent className="space-y-4">
        <div className="w-full max-w-lg space-y-5 mx-auto">
          <div className="mb-8">
            <Logo path="/" variant="dark" className="w-[155px]! mx-auto" />
            <h2 className="text-[36px] xl:text-[43px] font-bold text-[#555555] text-center mt-[30px] mb-[2px]">
              Sign Up
            </h2>
            <p className="text-center text-[#555555] text-base">
              Already have an account?{" "}
              <Link href={"/?login=true"} className="text-secondary underline">
                Log In
              </Link>
            </p>
          </div>
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <div className="flex flex-col md:flex-row items-center gap-4">
                <FormField
                  name="firstName"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="flex-1 w-full">
                      <FormLabel className="input-label">
                        First Name<span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Your first name" {...field} className="input-field" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  name="lastName"
                  control={form.control}
                  render={({ field }) => (
                    <FormItem className="flex-1 w-full">
                      <FormLabel className="input-label">
                        Last Name<span className="text-red-500">*</span>
                      </FormLabel>
                      <FormControl>
                        <Input placeholder="Your last name" {...field} className="input-field" />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                name="email"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Email<span className="text-red-500">*</span>
                    </FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                name="intent"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      I want to<span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full rounded-full py-7! pl-[28px] text-[#555555]">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        <SelectItem value="rent">Rent Available Space</SelectItem>
                        <SelectItem value="find">Find Available Space</SelectItem>
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                name="spaceType"
                control={form.control}
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">
                      Space Type<span className="text-red-500">*</span>
                    </FormLabel>
                    <Select onValueChange={field.onChange} defaultValue={field.value}>
                      <FormControl>
                        <SelectTrigger className="w-full rounded-full py-7! pl-[28px] text-[#555555]">
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {spaceTypes.map((spaceType, idx) => (
                          <SelectItem key={idx} value={spaceType.value}>
                            {spaceType.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <Button type="submit" className="w-full py-6">
                Continue
              </Button>
            </form>
          </Form>
          <div className="after:border-border relative text-center text-sm after:absolute after:inset-0 after:top-1/2 after:z-0 after:flex after:items-center after:border-t">
            <span className="bg-card text-muted-foreground relative z-10 px-2">OR</span>
          </div>
          <div className="space-y-4">
            <Button
              className="w-full py-6 text-[#555]"
              variant={"outline"}
              onClick={() => handleSocialRedirect(router, "google", "auth")}
            >
              <GoogleIcon /> Log In with Google
            </Button>
            <Button
              className="w-full py-6 text-[#555]"
              variant={"outline"}
              onClick={() => console.log("You clicked social button")}
            >
              <FaInstagram />
              Log In with Instagram
            </Button>
            <Button
              className="w-full py-6 text-[#555]"
              variant={"outline"}
              onClick={() => handleSocialRedirect(router, "facebook", "auth")}
            >
              <FacebookIcon /> Log In with Facebook
            </Button>
            <Button
              className="w-full py-6 text-[#555]"
              variant={"outline"}
              onClick={() => {
                console.log("You clicked social button");
              }}
            >
              <FaXTwitter />
              Log In with X
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
