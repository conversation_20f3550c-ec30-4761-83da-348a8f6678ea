"use client";

import React from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { Textarea } from "@/components/ui/textarea";
import { GuestPrefsData, guestPrefsSchema } from "@/lib/validations/profileForm.schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { UserProfile } from "@/typescript/interfaces";
import { updateUserProfile } from "@/actions/user.action";
import toast from "react-hot-toast";
import { useRouter } from "next/navigation";
import { useCookies } from "react-cookie";

interface Args {
  isEdit: boolean;
  setIsEdit: (value: boolean) => void;
  user: UserProfile;
}

const GuestPreferencesForm = ({ isEdit, setIsEdit, user }: Args) => {
  const [cookies] = useCookies(["token"]);
  const token = cookies.token;
  const router = useRouter();
  const form = useForm<GuestPrefsData>({
    resolver: zodResolver(guestPrefsSchema),
    defaultValues: {
      idealTenantDescription: user?.idealTenantDescription || "",
    },
  });

  const onSubmit = async (data: GuestPrefsData) => {
    const response = await updateUserProfile(data, token);
    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message);
      setIsEdit(!isEdit);
      router.refresh();
    } else {
      toast.error(response.message);
      setIsEdit(!isEdit);
      router.refresh();
    }
  };

  return (
    <div className="space-y-7 bg-white shadow p-4 rounded-md border">
      <div className="space-y-3 relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="user-round-search" size={24} /> Guest Preferences
        </h2>
      </div>
      <Form {...form}>
        <form className="space-y-5" onSubmit={form.handleSubmit(onSubmit)}>
          <FormField
            name="idealTenantDescription"
            control={form.control}
            render={({ field }) => (
              <FormItem>
                <FormLabel className="input-label">Ideal Tenant Description</FormLabel>
                <FormControl>
                  <Textarea {...field} placeholder="Ideal tenant description" />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
          <div className="w-full max-w-sm mx-auto ">
            <Button type="submit" className="w-full h-auto py-3 rounded-full cursor-pointer">
              Submit
            </Button>
          </div>
        </form>
      </Form>
    </div>
  );
};

export default GuestPreferencesForm;
