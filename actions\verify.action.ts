const baseURL = process.env.NEXT_PUBLIC_API_URL;

export const handleEmailVerify = async (token: string) => {
  try {
    const response = await fetch(`${baseURL}/web/auth/signup/verify-email`, {
      method: "PATCH",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify({ token }),
    });

    const resData = await response.json();
    return {
      ...resData,
      status: response.status,
    };
  } catch (error: unknown) {
    if (error instanceof Error) {
      return {
        message: error.message,
        status: 500,
      };
    } else {
      return {
        message: "An error occurred",
        status: 500,
      };
    }
  }
};
