"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { FindPrivateRoomData, findPrivateRoomSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  allowPets,
  findBathRooms,
  findBedroomSize,
  findParking,
  wantFurnished,
  willingToSignRentalAgreement,
} from "@/constants/constants";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
// import { GeoLocation } from "@/components/GeoLocation/GeoLocation";
import { TFormMode } from "@/typescript/types";
// import GoogleMapsProvider from "@/components/GoogleMapsProvider";
import { formatNumberWithCommas, isValidNumberInput, removeCommas } from "@/utils/numberFormatter";
import LocationAutocomplete from "@/components/LocationAutocomplete/LocationAutocomplete";
import GoogleMapsProvider from "@/components/GoogleMapsProvider";
import { Slider } from "@/components/ui/slider";
import { DynamicIcon } from "lucide-react/dynamic";

interface Args {
  mode: TFormMode;
}

const FindPrivateRoomStep = ({ mode }: Args) => {
  const router = useRouter();
  const { currentStep, steps, data, setCurrentStep, setUserData } = useSignupStore();
  const form = useForm<FindPrivateRoomData>({
    resolver: zodResolver(findPrivateRoomSchema),
    defaultValues: {
      preferredLocation: data.user.preferredLocation || [0, 0],
      preferredLocationLabel: data.user.preferredLocationLabel || "",
      rentalStartDate: data.user.rentalStartDate || "",
      rentalDuration: data.user.rentalDuration || 1,
      maxMonthlyBudget: data.user.maxMonthlyBudget || 0,
      willingToSignRentalAgreement: data.user.willingToSignRentalAgreement || "",
      wantFurnished: data.user.wantFurnished || "",
      bathroom: data.user.bathroom || "",
      bedroomSize: data.user.bedroomSize || "",
      pets: data.user.pets || "",
      parkingRequired: data.user.parkingRequired || "",
    },
  });

  const locationURL = mode === "add" ? "/dashboard/my-spaces/add" : "/dashboard/my-spaces/edit";

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`${locationURL}/${currentStep - 1}`);
  };

  const handleNext = () => {
    if (!form.formState.isValid) return;
    if (currentStep === steps.length - 1) return;
    setCurrentStep(currentStep + 1);
    router.push(`${locationURL}/${currentStep + 1}`);
  };

  function onSubmit(values: FindPrivateRoomData) {
    const preferredLocation: [number, number] = [
      values.preferredLocation[0],
      values.preferredLocation[1],
    ];
    setUserData({
      ...values,
      preferredLocation,
    });
    // setPropertyData(values);
    router.push(`${locationURL}/${currentStep + 1}`);
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="preferredLocation"
          control={form.control}
          render={({}) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Location<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <GoogleMapsProvider>
                  <LocationAutocomplete
                    className="input-field"
                    placeholder="Enter your location"
                    onSelect={(data) => {
                      form.setValue("preferredLocation", [data.latLng.lng, data.latLng.lat]);
                      form.setValue("preferredLocationLabel", data.address);
                    }}
                  />
                </GoogleMapsProvider>
                {/* <GoogleMapsProvider>
                  <GeoLocation
                    onSelect={(data) => {
                      form.setValue("preferredLocation", [data.latLng.lng, data.latLng.lat]);
                      form.setValue("preferredLocationLabel", data.address);
                    }}
                  />
                </GoogleMapsProvider> */}
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="rentalStartDate"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Rental Start Date<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Rental Start Date"
                  type="date"
                  {...field}
                  className="input-field"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="rentalDuration"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Rental Duration<span className="text-red-500">*</span>
              </FormLabel>
              {field.value <= 11 ? (
                <>
                  <FormControl className="mt-2">
                    <Slider
                      min={1}
                      step={1}
                      max={12}
                      value={[field.value || 1]}
                      onValueChange={(value) => field.onChange(value[0])}
                    />
                  </FormControl>
                  <p className="text-sm text-muted-foreground">{field.value || 1} Months</p>
                </>
              ) : (
                <FormControl>
                  <Select
                    defaultValue={field.value === 12 ? "12" : "11"}
                    onValueChange={(value) => field.onChange(parseInt(value))}
                  >
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select duration" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="11">Less than 1 Year</SelectItem>
                      <SelectItem value="12">1 Year</SelectItem>
                      <SelectItem value="13">1 Year+</SelectItem>
                    </SelectContent>
                  </Select>
                </FormControl>
              )}
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="maxMonthlyBudget"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Max Monthly Budget<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <div className="relative">
                  <DynamicIcon
                    name="dollar-sign"
                    size={16}
                    className="absolute top-1/2 left-3 -translate-y-1/2 text-gray-600"
                  />
                  <Input
                    placeholder="Max Monthly Budget"
                    {...field}
                    value={formatNumberWithCommas(field.value || "")}
                    onChange={(e) => {
                      const raw = removeCommas(e.target.value);
                      if (isValidNumberInput(raw)) {
                        field.onChange(raw);
                      }
                    }}
                    className="input-field pl-8"
                  />
                </div>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="willingToSignRentalAgreement"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Willing to Sign Rental Agreement<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {willingToSignRentalAgreement.map((agreement) => (
                      <SelectItem key={agreement.value} value={agreement.value}>
                        {agreement.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="wantFurnished"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Want Furnished<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {wantFurnished.map((item) => (
                      <SelectItem key={item.value} value={item.value}>
                        {item.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bathroom"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Bathroom<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {findBathRooms.map((bathRoom, idx) => (
                      <SelectItem key={idx} value={bathRoom.value}>
                        {bathRoom.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bedroomSize"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Bedroom Size<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {findBedroomSize.map((bedRoom) => (
                      <SelectItem key={bedRoom.value} value={bedRoom.value}>
                        {bedRoom.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="pets"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Pets?<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger className="w-full rounded-full py-6! pl-6">
                      <SelectValue placeholder="Select" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    {allowPets.map((pet) => (
                      <SelectItem key={pet.value} value={pet.value}>
                        {pet.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="parkingRequired"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Parking Required<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {findParking.map((parking) => (
                    <SelectItem key={parking.value} value={parking.value}>
                      {parking.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" onClick={handleNext} className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Complete" : "Next"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default FindPrivateRoomStep;
