"use client";

import React from "react";
import { useJsApiLoader } from "@react-google-maps/api";
import { Skeleton } from "@/components/ui/skeleton";

const libraries: ("places" | "geometry")[] = ["places", "geometry"];

const GoogleMapsProvider = ({
  children,
  fallback,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}) => {
  const { isLoaded, loadError } = useJsApiLoader({
    googleMapsApiKey: process.env.NEXT_PUBLIC_GOOGLE_API_KEY as string,
    libraries,
  });

  if (loadError) {
    console.error("Google Maps API Load Error:", loadError);
    return <div>Failed to load map 😢</div>;
  }

  if (!isLoaded) return fallback || <Skeleton className="h-14 w-full" />;

  return <>{children}</>;
};

export default GoogleMapsProvider;
