"use client";

import React from "react";
import { CookiesProvider } from "react-cookie";

interface Args {
  children: React.ReactNode;
}

const ClientWrapper = ({ children }: Args) => {
  return (
    <CookiesProvider
      defaultSetOptions={{
        path: "/",
        secure: false,
        maxAge: 30 * 24 * 60 * 60, // 30 days
      }}
    >
      {children}
    </CookiesProvider>
  );
};

export default ClientWrapper;
