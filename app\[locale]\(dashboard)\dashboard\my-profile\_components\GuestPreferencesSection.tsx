"use client";

import React, { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import GuestPreferencesForm from "./GuestPreferencesForm";
import { UserProfile } from "@/typescript/interfaces";

interface Args {
  user: UserProfile;
}

const GuestPreferencesSection = ({ user }: Args) => {
  const [isEdit, setIsEdit] = useState<boolean>(false);
  return isEdit ? (
    <GuestPreferencesForm isEdit={isEdit} setIsEdit={setIsEdit} user={user} />
  ) : (
    <div className="space-y-7 bg-white shadow p-4 rounded-md border">
      <div className="space-y-3 relative">
        <div className="absolute top-0 right-0">
          <Button
            variant={"ghost"}
            size={"icon"}
            className="cursor-pointer"
            onClick={() => setIsEdit(!isEdit)}
          >
            <DynamicIcon name="square-pen" size={20} color="#8E44AD" />
          </Button>
        </div>
        <h2 className="text-[22px] font-bold text-secondary flex items-center gap-2.5">
          <DynamicIcon name="user-round-search" size={24} /> Guest Preferences
        </h2>
      </div>
      <div>
        <p>{user?.idealTenantDescription}</p>
      </div>
    </div>
  );
};

export default GuestPreferencesSection;
