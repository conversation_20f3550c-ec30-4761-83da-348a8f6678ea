import React from "react";
import { Skeleton } from "../ui/skeleton";

const FacesSkeleton = () => {
  return (
    <div className="flex flex-col space-y-3">
      <div className="flex justify-between items-center gap-4">
        <Skeleton className="h-[34px] w-[112px] rounded-md" />
        <Skeleton className="h-[34px] w-[112px] rounded-md" />
      </div>
      <div>
        <Skeleton className="h-[173px] w-[173px] rounded-full mx-auto" />
        <Skeleton className="h-4 w-[200px] mx-auto mt-4" />
        <Skeleton className="h-4 w-[150px] mx-auto mt-2" />
      </div>
      <div className="flex justify-between items-center gap-4">
        <Skeleton className="h-[40px] w-[100px]" />
        <Skeleton className="h-[40px] w-[100px]" />
      </div>
    </div>
  );
};

export default FacesSkeleton;
