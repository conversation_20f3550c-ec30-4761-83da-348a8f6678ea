import React from "react";
import Image from "next/image";
import { DynamicIcon, dynamicIconImports } from "lucide-react/dynamic";

interface IFeaturedCard {
  icon: keyof typeof dynamicIconImports;
  title: string;
  description: string;
  image: string;
  reverse?: boolean;
}

const FeaturedCard = ({ icon, title, description, image, reverse = false }: IFeaturedCard) => {
  return (
    <article
      className={`flex flex-auto flex-col md:flex-row items-center gap-6 xl:gap-[35px] ${reverse ? "md:flex-row-reverse" : ""}`}
    >
      <figure className="w-full md:w-auto">
        <Image
          src={image}
          alt={`${title} Image`}
          width={800}
          height={800}
          priority
          className="w-full lg:w-[500px]"
        />
      </figure>
      <div className="w-full flex-2 space-y-5">
        <DynamicIcon name={icon} size={40} className="text-primary" />
        <h2 className="text-3xl xl:text-[50px] font-bold text-secondary">{title}</h2>
        <p className="text-gray-600 text-base lg:text-lg xl:text-[25px] leading-relaxed">
          {description}
        </p>
      </div>
    </article>
  );
};

export default FeaturedCard;
