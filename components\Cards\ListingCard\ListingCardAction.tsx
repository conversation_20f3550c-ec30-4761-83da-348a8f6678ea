"use client";

import React from "react";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import Link from "next/link";
import { handleListingDelete } from "@/actions/spaces.action";
import { useRouter } from "next/navigation";
import toast from "react-hot-toast";

interface ListingCardActionProps {
  listingId: string;
}

const ListingCardAction = ({ listingId }: ListingCardActionProps) => {
  const router = useRouter();
  const handleDeleteListing = async () => {
    const response = await handleListingDelete(listingId);
    if (response.status >= 200 && response.status < 300) {
      toast.success(response.message);
      router.refresh();
    } else {
      toast.error(response.message);
    }
  };
  return (
    <div className="flex items-center gap-2">
      <Button size={"icon"} variant={"outline"} className="border-primary">
        <Link href={`/dashboard/my-spaces/edit/${listingId}/0`}>
          <DynamicIcon name="square-pen" size={20} />
        </Link>
      </Button>
      <Button
        size={"icon"}
        variant={"outline"}
        className="border-primary"
        onClick={handleDeleteListing}
      >
        <DynamicIcon name="trash-2" size={20} />
      </Button>
    </div>
  );
};

export default ListingCardAction;
