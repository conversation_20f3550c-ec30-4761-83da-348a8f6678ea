import React from "react";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import BlogCard from "@/components/Cards/BlogCard/BlogCard";
import { Blog } from "@/typescript/types";

interface BlogPageSectionProps {
  blogs: Blog[];
}

const BlogPageSection = ({ blogs }: BlogPageSectionProps) => {
  return (
    <React.Fragment>
      <TopBanner title="Blog" />
      <section>
        <div className="container">
          {blogs.length > 0 ? (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-5">
              {blogs.map((blog: Blog) => (
                <BlogCard key={blog._id} blog={blog} />
              ))}
            </div>
          ) : (
            <p>No Blogs Found</p>
          )}
        </div>
      </section>
    </React.Fragment>
  );
};

export default BlogPageSection;
