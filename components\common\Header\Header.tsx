"use client";

import React from "react";
import Link from "next/link";
import Logo from "../Logo";
import { offcanvasNavigations } from "@/data";
import { Navigation, IOffcanvasNavigation } from "@/typescript/interfaces";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";
import { <PERSON><PERSON>, <PERSON>et<PERSON>ontent, <PERSON>etHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet";
import { authStore } from "@/store/authStore";
import UserNavigation from "@/components/UserNavigation/UserNavigation";
import LanguageSelector from "@/components/LanguageSelector/LanguageSelector";
import { useTranslations } from "next-intl";

const Header = () => {
  const { isAuthenticated } = authStore((state) => state);
  const t = useTranslations("Header");
  const navigations = t.raw("Navigations");

  return (
    <header className="bg-white border-b px-4">
      {/* <div className="container"> */}
      <div className="flex justify-between items-center gap-4 lg:gap-7">
        <Logo className="w-[155]! h-[45]!" variant="dark" />
        <div className="flex justify-end md:justify-between items-center lg:flex-1 gap-7">
          <ul className="hidden lg:flex items-center gap-4 lg:gap-7">
            {navigations.map((navigation: Navigation, idx: number) => (
              <li key={idx}>
                <Link
                  href={navigation.href}
                  className="text-base lg:text-lg font-medium text-gray-500 hover:text-secondary"
                >
                  {navigation.label}
                </Link>
              </li>
            ))}
          </ul>
          <div className="flex items-center gap-4 lg:gap-6">
            <LanguageSelector />
            {isAuthenticated ? (
              <Button
                asChild
                className="shadow-md rounded-full p-5"
                variant={"outline"}
                size={"icon"}
              >
                <Link href={"/dashboard/messages"}>
                  <DynamicIcon name="message-square-share" size={16} />
                </Link>
              </Button>
            ) : (
              <Button asChild className="hidden sm:flex rounded-full drop-shadow-lg">
                <Link href={"/auth/register"}>
                  <DynamicIcon name="house-plus" />
                  List Your Space for Free
                </Link>
              </Button>
            )}
            {isAuthenticated && <UserNavigation />}
            <OffcanvasNavigation />
            <MobileNavigation />
          </div>
        </div>
      </div>
      {/* </div> */}
    </header>
  );
};

// Off Canvas Navigation
const OffcanvasNavigation = () => {
  const [open, setOpen] = React.useState(false);
  const { isAuthenticated } = authStore((state) => state);
  return (
    <Sheet open={open} onOpenChange={setOpen}>
      <SheetTrigger className="block cursor-pointer">
        <DynamicIcon name="align-left" size={34} />
      </SheetTrigger>
      <SheetContent>
        <SheetHeader className="mt-[60px] ml-[35px]">
          <SheetTitle onClick={() => setOpen(false)}>
            <Logo className="w-40!" />
          </SheetTitle>
        </SheetHeader>
        <div className="flex flex-col gap-4 px-4 pb-4 mx-[35px]">
          <ul className="space-y-4">
            {offcanvasNavigations.map((navigation: IOffcanvasNavigation, idx: number) => (
              <li key={idx}>
                <Link
                  href={navigation.href}
                  className="text-lg text-[#555555] hover:text-primary inline-flex items-center gap-4 pr-2 py-2"
                  onClick={() => setOpen(false)}
                >
                  <DynamicIcon name={navigation.icon} size={24} color="#555555" />
                  {navigation.label}
                </Link>
              </li>
            ))}
            {!isAuthenticated && (
              <Button
                asChild
                className="w-full rounded-full py-6 text-lg font-medium shadow-md"
                onClick={() => setOpen(false)}
              >
                <Link href={"/auth/login"}>
                  <DynamicIcon name="log-in" size={20} /> Login
                </Link>
              </Button>
            )}
          </ul>
        </div>
      </SheetContent>
    </Sheet>
  );
};

const MobileNavigation = () => {
  const { isAuthenticated } = authStore((state) => state);
  return (
    <div className="block md:hidden fixed left-0 right-0 bottom-2 p-2 bg-secondary z-30 rounded-full mx-2.5 border border-gray-100/10 drop-shadow-lg">
      <ul className="flex items-center justify-between">
        <li>
          <Link
            href={"/explore-spaces"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-white"
          >
            <DynamicIcon name="house-plus" size={20} />
          </Link>
        </li>
        <li>
          <Link
            href={"/explore-faces"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-white"
          >
            <DynamicIcon name="map-pin-house" size={20} />
          </Link>
        </li>
        <li>
          <Link
            href={"/"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-white"
          >
            <DynamicIcon name="home" size={30} />
          </Link>
        </li>
        {/* <li>
          <Link
            href={"#"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-white"
          >
            <DynamicIcon name="heart" size={20} />
          </Link>
        </li> */}
        <li>
          <Link
            href={"#"}
            className="w-10 h-10 rounded-md flex items-center justify-center text-white"
          >
            <DynamicIcon name="message-square-share" size={20} />
          </Link>
        </li>
        <li>
          {!isAuthenticated ? (
            <Button
              asChild
              size={"icon"}
              variant={"ghost"}
              className="w-10 h-10 rounded-md flex items-center justify-center text-white"
            >
              <Link href={"/auth/login"}>
                <DynamicIcon name="log-in" size={20} />
              </Link>
            </Button>
          ) : (
            <UserNavigation />
          )}
        </li>
      </ul>
    </div>
  );
};

export default Header;
