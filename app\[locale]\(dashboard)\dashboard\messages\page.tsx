import React from "react";
import { DynamicIcon } from "lucide-react/dynamic";

const MessagePage = () => {
  return (
    <section className="h-[647px] border bg-white p-4 rounded-lg flex flex-col justify-center items-center gap-4 shadow">
      <DynamicIcon name="message-circle-more" size={50} color="#99a1af" />
      <p className="text-gray-400">Select a conversation to start messaging.</p>
    </section>
  );
};

export default MessagePage;
