import React from "react";
import SpacesSkeleton from "@/components/Skeletons/SpacesSkeleton";

const loading = () => {
  return (
    <section>
      <div className="container">
        <div className="grid grid-cols-12 gap-5">
          <div className="col-span-12 lg:col-span-8">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-5">
              {Array.from({ length: 10 }).map((_, idx) => (
                <SpacesSkeleton key={idx} />
              ))}
            </div>
          </div>
          <div className="col-span-12 lg:col-span-4"></div>
        </div>
      </div>
    </section>
  );
};

export default loading;
