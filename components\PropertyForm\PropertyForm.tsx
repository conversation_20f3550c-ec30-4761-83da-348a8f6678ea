"use client";

import React, { useEffect, useMemo } from "react";
import { useSignupStore } from "@/store/userSignUpStore";
import { usePara<PERSON>, useRouter } from "next/navigation";
import {
  ABOUT_YOU,
  RENTAL_BASICS,
  RENTAL_PREFERENCES,
  ROOMMATE_PREFERENCES,
  TENANT_PREFERENCES,
  THE_HOME,
  THE_ROOM,
  THE_SPACE,
  UPLOAD_PROFILE_PHOTO,
  UPLOAD_SPACE_PHOTOS,
} from "@/constants/auth.constants";
import AboutYouStep from "./_components/AboutYouStep";
import ProfilePhotoStep from "./_components/ProfilePhotoStep";
import HomeStep from "./_components/HomeStep";
import RoomStep from "./_components/RoomStep";
import SpaceStep from "./_components/SpaceStep";
import SpacePhotosStep from "./_components/SpacePhotosStep";
import RoommatePreferencesStep from "./_components/RoommatePreferencesStep";
import RentalBasicsStep from "./_components/RentalBasicsStep";
import TenantPreferencesStep from "./_components/TenantPreferencesStep";
import RentalPreferencesStep from "./_components/RentalPreferencesStep";
import AboutYouEntireStep from "./_components/AboutYouEntireStep";
import AboutYouFindStep from "./_components/AboutYouFindStep";
import FindPrivateRoomStep from "./_components/FindPrivateRoomStep";
import FindRoommatePreferencesStep from "./_components/FindRoommatePreferecesStep";
import { PropertyData, UserProfile } from "@/typescript/interfaces";
import { TFormMode } from "@/typescript/types";
import { getListingSteps } from "@/lib/getOnboardingSteps";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface Args {
  mode: TFormMode;
  user: UserProfile;
  listing?: PropertyData;
}

const PropertyForm = ({ user, mode = "add", listing }: Args) => {
  const { step } = useParams();
  const router = useRouter();
  const currentStep = parseInt(step as string);

  const steps = useMemo(() => {
    return getListingSteps(user?.intent as string, user?.spaceType as string);
  }, [user?.intent, user?.spaceType]);

  const {
    data,
    setCurrentStep,
    currentStep: storeCurrentStep,
    setUserData,
    setSteps,
  } = useSignupStore();

  // eslint-disable-next-line react-hooks/exhaustive-deps
  useEffect(() => {
    setSteps(steps);
  }, [steps, setSteps]);

  useEffect(() => {
    // If user is trying to access a step beyond their current progress,
    // redirect them to their current step to prevent skipping steps
    // if (currentStep > storeCurrentStep && storeCurrentStep >= 0) {
    //   router.push(`/auth/onboarding/${storeCurrentStep}`);
    //   return;
    // }

    // Update the store's current step to match the URL
    setCurrentStep(currentStep);
    setUserData(user);
  }, [currentStep, storeCurrentStep, user, setUserData, setCurrentStep]);

  useEffect(() => {
    if (typeof currentStep === "number" && currentStep < 0) {
      router.replace("/dashboard/my-spaces");
    }
  }, [currentStep, router]);

  const stepItem = steps[currentStep];
  const stepLabel = stepItem?.label;

  // Map step names to components
  const renderStepForm = () => {
    switch (stepLabel) {
      case ABOUT_YOU:
        return data.user.intent === "rent" ? (
          data.user.spaceType === "private_room" ? (
            <AboutYouStep />
          ) : (
            <AboutYouEntireStep user={user} mode={mode} />
          )
        ) : (
          <AboutYouFindStep />
        );
      case UPLOAD_PROFILE_PHOTO:
        return <ProfilePhotoStep mode={mode} user={user} />;
      case THE_HOME:
        return <HomeStep mode={mode} listing={listing} />;
      case THE_ROOM:
        return data.user.intent === "rent" ? (
          data.user.spaceType === "private_room" ? (
            <RoomStep mode={mode} listing={listing} />
          ) : (
            <p>Entire Room Step</p>
          )
        ) : (
          <FindPrivateRoomStep mode={mode} />
        );
      case THE_SPACE:
        return <SpaceStep mode={mode} listing={listing} />;
      case UPLOAD_SPACE_PHOTOS:
        return <SpacePhotosStep mode={mode} listing={listing} />;
      case ROOMMATE_PREFERENCES:
        return data.user.intent === "rent" ? (
          data.user.spaceType === "private_room" ? (
            <RoommatePreferencesStep mode={mode} listing={listing} />
          ) : (
            <p>Entire Roommate Preferences Step</p>
          )
        ) : (
          <FindRoommatePreferencesStep mode={mode} />
        );
      case RENTAL_BASICS:
        return <RentalBasicsStep mode={mode} listing={listing} />;
      case TENANT_PREFERENCES:
        return <TenantPreferencesStep mode={mode} listing={listing} />;
      case RENTAL_PREFERENCES:
        return <RentalPreferencesStep />;
      default:
        return <div>Form not found for step: {stepLabel}</div>;
    }
  };

  return (
    <div>
      <Card>
        <CardHeader className="px-6 lg:px-10">
          <CardTitle className="text-[27px] text-secondary font-semibold flex justify-center items-center gap-2.5 mt-[27px]">
            {stepItem?.label}
          </CardTitle>
        </CardHeader>
        <CardContent className="px-6 lg:px-[87px] pb-[30px]">{renderStepForm()}</CardContent>
      </Card>
    </div>
  );
};

export default PropertyForm;
