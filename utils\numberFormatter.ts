/**
 * Format number with comma separators.
 * Example: 1000000 => "1,000,000"
 */
export const formatNumberWithCommas = (value: string | number) => {
  const cleaned = value.toString().replace(/,/g, "");
  const num = parseFloat(cleaned);
  if (isNaN(num)) return "";
  return num.toLocaleString("en-US");
};

/**
 * Remove commas from formatted number
 * Example: "1,000,000" => "1000000"
 */
export const removeCommas = (value: string | number) => {
  return value.toString().replace(/,/g, "");
};

/**
 * Check if string is a valid number (with optional decimals)
 */
export const isValidNumberInput = (value: string): boolean => {
  return /^\d*\.?\d*$/.test(value.replace(/,/g, ""));
};
