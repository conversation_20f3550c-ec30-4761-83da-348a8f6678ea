"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  allowedGuests,
  allowPets,
  amenities,
  bathRooms,
  bedRooms,
  neighthborhoods,
  numberOfOccupants,
  ownerOccupied,
  parkings,
  residenceTypes,
  smokeCigarettes,
  smokeMarijuana,
} from "@/constants/constants";
import { Textarea } from "@/components/ui/textarea";
import { HomeData, homeSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { MultiSelect } from "@/components/ui/MultiSelect";
// import { GeoLocation } from "@/components/GeoLocation/GeoLocation";
import { TFormMode } from "@/typescript/types";
import { PropertyData } from "@/typescript/interfaces";
// import GoogleMapsProvider from "@/components/GoogleMapsProvider";
import LocationAutocomplete from "@/components/LocationAutocomplete/LocationAutocomplete";
import GoogleMapsProvider from "@/components/GoogleMapsProvider";
import { formatNumberWithCommas, isValidNumberInput, removeCommas } from "@/utils/numberFormatter";

interface Args {
  mode: TFormMode;
  listing?: PropertyData;
}

const HomeStep = ({ mode, listing }: Args) => {
  const router = useRouter();
  const { currentStep, steps, setPropertyData, data, setCurrentStep, markStepCompleted } =
    useSignupStore();
  const form = useForm<HomeData>({
    resolver: zodResolver(homeSchema),
    defaultValues: {
      fullAddress: listing?.fullAddress || data.property.fullAddress || "",
      residenceType: listing?.residenceType || data.property.residenceType || "",
      size: listing?.size || data.property.size || "",
      bedrooms: listing?.bedrooms || data.property.bedrooms || "",
      bathrooms: listing?.bathrooms || data.property.bathrooms || "",
      ownerOccupied: listing?.ownerOccupied || data.property?.ownerOccupied?.toString() || "",
      numPeopleInHome: listing?.numPeopleInHome || data.property.numPeopleInHome || "",
      amenities: listing?.amenities || data.property.amenities || [],
      parking: listing?.parking || data.property.parking || [],
      neighborhood: listing?.neighborhood || data.property.neighborhood || [],
      currentPets: listing?.currentPets || data.property.currentPets || [],
      allowedPets: listing?.allowedPets || data.property.allowedPets || [],
      smokeCigarettes: listing?.smokeCigarettes || data.property.smokeCigarettes || "",
      smokeMarijuana: listing?.smokeMarijuana || data.property.smokeMarijuana || "",
      overnightGuestsAllowed:
        listing?.overnightGuestsAllowed || data.property.overnightGuestsAllowed || "",
      homeDescription: listing?.homeDescription || data.property.homeDescription || "",
      location:
        data.property.location && "coordinates" in data.property.location
          ? data.property.location.coordinates
          : data.property.location || [0, 0],
      locationLabel: listing?.locationLabel || data.property.locationLabel || "",
    },
  });

  const locationURL =
    mode === "add" ? "/dashboard/my-spaces/add" : `/dashboard/my-spaces/edit/${listing?._id}`;

  function onSubmit(values: any) {
    setPropertyData(values);
    markStepCompleted(currentStep);
    setCurrentStep(currentStep + 1);
    router.push(`${locationURL}/${currentStep + 1}`);
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`${locationURL}/${currentStep - 1}`);
  };

  const handleNext = () => {
    // Check schema is valid
    if (!form.formState.isValid) return;
    if (currentStep === steps.length - 1) return;
    markStepCompleted(currentStep);
    setCurrentStep(currentStep + 1);
    router.push(`${locationURL}/${currentStep + 1}`);
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="fullAddress"
          control={form.control}
          render={({}) => (
            <FormItem>
              <FormLabel className="input-label">
                Full Address<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <GoogleMapsProvider>
                  <LocationAutocomplete
                    className="input-field"
                    placeholder="Enter your location"
                    onSelect={(data) => {
                      form.setValue("fullAddress", data.address);
                      form.setValue("location", [data.latLng.lng, data.latLng.lat]);
                      form.setValue("locationLabel", data.address);
                    }}
                  />
                </GoogleMapsProvider>
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="residenceType"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Residence Type<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {residenceTypes.map((residence, idx) => (
                    <SelectItem key={idx} value={residence.value}>
                      {residence.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="size"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                <span>
                  Size ft<sup>2</sup>
                </span>
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Input
                  placeholder="Size"
                  {...field}
                  value={formatNumberWithCommas(field.value || "")}
                  onChange={(e) => {
                    const raw = removeCommas(e.target.value);
                    if (isValidNumberInput(raw)) {
                      field.onChange(raw);
                    }
                  }}
                  className="input-field"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bedrooms"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Number of Bedrooms<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bedRooms.map((bedRoom, idx) => (
                    <SelectItem key={idx} value={bedRoom.value}>
                      {bedRoom.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="bathrooms"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Number of Bathrooms<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {bathRooms.map((bathRoom, idx) => (
                    <SelectItem key={idx} value={bathRoom.value}>
                      {bathRoom.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="ownerOccupied"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Owner Occupied (do you live in the home)?<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {ownerOccupied.map((owner, idx) => (
                    <SelectItem key={idx} value={owner.value}>
                      {owner.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="numPeopleInHome"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Total number of people in the home (including the new roommate)?
                <span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {numberOfOccupants.map((occupant, idx) => (
                    <SelectItem key={idx} value={occupant.value}>
                      {occupant.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="amenities"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Aminities<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={amenities}
                  defaultValue={field.value}
                  value={field.value}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="parking"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Parking<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={parkings}
                  defaultValue={field.value}
                  value={field.value}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="neighborhood"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Neighborhood<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={neighthborhoods}
                  defaultValue={field.value}
                  value={field.value}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="currentPets"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Current Pets<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={allowPets}
                  defaultValue={field.value}
                  value={field.value}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="allowedPets"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Allowed Pets<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect
                  options={allowPets}
                  defaultValue={field.value}
                  value={field.value}
                  onValueChange={field.onChange}
                  className="pl-6!"
                />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeCigarettes"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Smoke Cigarettes<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeCigarettes.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="smokeMarijuana"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Smoke Marijuana<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {smokeMarijuana.map((smoke, idx) => (
                    <SelectItem key={idx} value={smoke.value}>
                      {smoke.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="overnightGuestsAllowed"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Overnight Guests Allowed?<span className="text-red-500">*</span>
              </FormLabel>
              <Select onValueChange={field.onChange} defaultValue={field.value} value={field.value}>
                <FormControl>
                  <SelectTrigger className="w-full rounded-full py-6! pl-6">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                </FormControl>
                <SelectContent>
                  {allowedGuests.map((guest, idx) => (
                    <SelectItem key={idx} value={guest.value}>
                      {guest.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="homeDescription"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Home Description (note: all selected features will already be displayed)
                <span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <Textarea placeholder="Home Description" {...field} className="h-40" />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" onClick={handleNext} className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Complete" : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default HomeStep;
