import { cookies } from "next/headers";
import { redirect } from "next/navigation";

type IsAuthenticatedOptions = {
  noRedirect?: boolean;
  from?: string;
};

export default async function isAuthenticated(options?: IsAuthenticatedOptions) {
  const cookieStore = await cookies();
  const baseUrl = process.env.NEXT_PUBLIC_API_URL;
  const token = cookieStore.get("token")?.value;

  if (!token) {
    if (!options?.noRedirect) {
      redirect("/");
    }
    return;
  }

  try {
    const response = await fetch(baseUrl + "/web/user/me", {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
        "Content-Type": "application/json",
      },
      credentials: "include",
      cache: "no-store",
    });

    const user = (await response.json())?.user;

    return {
      user: user,
    };
  } catch (err: unknown) {
    console.error("isAuthenticated error: ", err);
    if (err instanceof Error) {
      console.error("isAuthenticated error: ", err.message);
    }
    if (!options?.noRedirect) {
      redirect("/");
    }
  }
}
