"use client";

import Image from "next/image";
import { PropertyData } from "@/typescript/interfaces";
import { Swiper, SwiperSlide } from "swiper/react";
import { Navigation } from "swiper/modules";
import { ImageType } from "@/typescript/types";

interface ListingImagesSlideProps {
  listing: PropertyData;
}

const ListingImagesSlide = ({ listing }: ListingImagesSlideProps) => {
  return (
    <Swiper
      modules={[Navigation]}
      navigation={true}
      className="h-[315px] md:h-[500px] lg:h-[630px]"
    >
      {listing.photos.map((image: ImageType, idx: number) => {
        const imageUrl =
          process.env.NEXT_PUBLIC_CDN_URL + "/" + image.filePath + "/" + image.fileName;
        return (
          <SwiperSlide key={idx}>
            <Image
              src={imageUrl}
              alt={`Image ${idx}`}
              width={800}
              height={800}
              priority
              className="w-full h-full object-cover rounded-md"
            />
          </SwiperSlide>
        );
      })}
    </Swiper>
  );
};

export default ListingImagesSlide;
