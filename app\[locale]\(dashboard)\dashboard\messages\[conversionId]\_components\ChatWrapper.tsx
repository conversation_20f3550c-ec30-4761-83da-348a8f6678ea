"use client";

import React, { useEffect } from "react";
import <PERSON><PERSON><PERSON>ead<PERSON> from "./ChatHeader";
import { ScrollArea } from "@/components/ui/scroll-area";
import MessageGroup from "./ChatGroup";
import MessageInput from "./MessageInput";
import { Message } from "@/typescript/interfaces";
import { useCookies } from "react-cookie";
import { getSocket } from "@/socket/socket";
import { useParams } from "next/navigation";
import { isMessageFromCurrentUser, normalizeMessage } from "@/utils/chatUtils";

interface MessageSender {
  name: string;
  avatar: string;
  isCurrentUser: boolean;
}

interface Args {
  currentUser: MessageSender;
  otherUser: MessageSender;
  chats: Message[];
  isCurrentUser: string; // This is the current user's ID
}

const ChatWrapper = ({ currentUser, otherUser, chats: initialChats, isCurrentUser }: Args) => {
  const [cookies] = useCookies(["token"]);
  const params = useParams();
  const token = cookies.token;
  const [chats, setChats] = React.useState<Message[]>(initialChats);
  const conversationId = params?.conversionId as string;
  const messagesEndRef = React.useRef<HTMLDivElement>(null);
  const scrollAreaRef = React.useRef<HTMLDivElement>(null);

  // Scroll to bottom when messages change
  useEffect(() => {
    // Instead of scrolling the entire page, scroll only within the ScrollArea
    if (messagesEndRef.current) {
      const scrollContainer = scrollAreaRef.current?.querySelector(
        "[data-radix-scroll-area-viewport]",
      );
      if (scrollContainer) {
        scrollContainer.scrollTop = scrollContainer.scrollHeight;
      }
    }
  }, [chats]);

  useEffect(() => {
    if (!token) return; // wait for token

    const socketInstance = getSocket(token);

    socketInstance.on("connect", () => {
      console.log("Connected to chat server ✅");
      socketInstance.emit("join", { conversationId });
    });

    socketInstance.on("message:new", (message: Message) => {
      console.log("New message received:", message);
      // Normalize the incoming message to ensure consistent user identification
      const normalizedMessage = normalizeMessage(message, isCurrentUser);
      setChats((prevChats) => [...prevChats, normalizedMessage]);
    });

    socketInstance.on("error", (error) => {
      console.log("Socket error:", error);
    });

    socketInstance.on("connect_error", (error) => {
      console.log("Connection error:", error);
    });

    return () => {
      socketInstance.off("connect");
      socketInstance.off("message:new");
      socketInstance.off("error");
      socketInstance.off("connect_error");
    };
  }, [token, conversationId]);

  // Sort messages by creation date
  const sortedChats = [...chats].sort((a, b) => {
    const dateA = a.createdAt ? new Date(a.createdAt).getTime() : 0;
    const dateB = b.createdAt ? new Date(b.createdAt).getTime() : 0;
    return dateA - dateB;
  });

  // Add this function to ensure consistent user ID handling
  const handleNewMessage = (newMessage: Message) => {
    // Normalize the message to ensure consistent user identification
    const normalizedMessage = normalizeMessage(newMessage, isCurrentUser);
    setChats((prev) => [...prev, normalizedMessage]);
  };

  return (
    <div className="rounded-lg shadow-md bg-white w-full">
      <ChatHeader name={otherUser.name} status="Has Available Space" avatar={otherUser.avatar} />
      <ScrollArea className="h-[480px]" ref={scrollAreaRef}>
        <div className="p-4 space-y-6">
          {sortedChats.map((chat, idx) => {
            // Determine if the message is from the current user using utility function
            const isFromCurrentUser = isMessageFromCurrentUser(chat, isCurrentUser);

            return (
              <MessageGroup
                key={idx}
                sender={isFromCurrentUser ? currentUser : otherUser}
                chat={chat}
              />
            );
          })}
          <div ref={messagesEndRef} />
        </div>
      </ScrollArea>
      <MessageInput onMessageSent={handleNewMessage} isCurrentUser={isCurrentUser} />
    </div>
  );
};

export default ChatWrapper;
