"use client";

import React from "react";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { useForm } from "react-hook-form";
import {
  ageRanges,
  preferredGenders,
  preferredSexualOrientations,
  smokingHabits,
} from "@/constants/constants";
import { useRouter } from "next/navigation";
import { useSignupStore } from "@/store/userSignUpStore";
import { Button } from "@/components/ui/button";
import { RoommateFindPrefsData, roommateFindPrefsSchema } from "@/lib/validations/sign-up-schema";
import { zodResolver } from "@hookform/resolvers/zod";
import { handleSignup } from "@/actions/auth.action";
import toast from "react-hot-toast";
import { MultiSelect } from "@/components/ui/MultiSelect";

const FindRoommatePreferencesStep = () => {
  const router = useRouter();
  const {
    currentStep,
    steps,
    data,
    setCurrentStep,
    clear,
    setUserD<PERSON>,
    markStepCompleted,
    resetCompletedSteps,
  } = useSignupStore();
  const form = useForm<RoommateFindPrefsData>({
    resolver: zodResolver(roommateFindPrefsSchema),
    defaultValues: {
      preferredGenderIdentity: data.user.preferredGenderIdentity || [],
      preferredSexualOrientation: data.user.preferredSexualOrientation || [],
      preferredAgeRange: data.user.preferredAgeRange || [],
      preferredSmokingHabits: data.user.preferredSmokingHabits || [],
    },
  });

  async function onSubmit(values: RoommateFindPrefsData) {
    setUserData(values);
    const fullDataToSend = {
      ...data,
      user: {
        ...data.user,
        ...values,
      },
    };
    // const isLastStep = currentStep === steps.length - 1;
    // if (isLastStep) {
    //   router.push("/");
    //   clear();
    // } else {
    //   router.push(`/auth/onboarding/${currentStep + 1}`);
    // }
    const res = await handleSignup(fullDataToSend);
    if (res.status >= 200 && res.status < 300) {
      toast.success(res.message);
      markStepCompleted(currentStep);
      resetCompletedSteps();
      setCurrentStep(0);
      clear();
      router.replace("/auth/thank-you");
    } else {
      toast.error(res.message);
      router.replace("/");
      setCurrentStep(0);
      resetCompletedSteps();
      clear();
    }
  }

  const handlePrevious = () => {
    if (currentStep === 0) {
      router.push("/auth/register");
    }
    setCurrentStep(currentStep - 1);
    router.push(`/auth/onboarding/${currentStep - 1}`);
  };

  // const handleNext = () => {
  //   // Check schema is valid
  //   if (!form.formState.isValid) return;
  //   if (currentStep === steps.length - 1) return;
  //   setCurrentStep(currentStep + 1);
  //   router.push(`/auth/onboarding/${currentStep + 1}`);
  // };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        <FormField
          name="preferredGenderIdentity"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Gender Identity<span className="text-red-500">*</span>
              </FormLabel>
              {/* Single Select */}
              <FormControl>
                <MultiSelect options={preferredGenders} onValueChange={field.onChange} />
              </FormControl>

              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredSexualOrientation"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Sexual Orientation<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={preferredSexualOrientations} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredAgeRange"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Age Range<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={ageRanges} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <FormField
          name="preferredSmokingHabits"
          control={form.control}
          render={({ field }) => (
            <FormItem>
              <FormLabel className="input-label">
                Preferred Smoking Habits<span className="text-red-500">*</span>
              </FormLabel>
              <FormControl>
                <MultiSelect options={smokingHabits} onValueChange={field.onChange} />
              </FormControl>
              <FormMessage />
            </FormItem>
          )}
        />
        <div className="flex justify-between gap-6 mt-8">
          <Button
            type="button"
            variant="outline"
            onClick={handlePrevious}
            className="flex-1 !py-6 cursor-pointer border-primary"
          >
            Previous
          </Button>
          <Button type="submit" className="flex-1 !py-6 cursor-pointer">
            {currentStep === steps.length - 1 ? "Complete" : "Continue"}
          </Button>
        </div>
      </form>
    </Form>
  );
};

export default FindRoommatePreferencesStep;
