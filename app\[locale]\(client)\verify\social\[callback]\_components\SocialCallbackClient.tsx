"use client";

import { useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { socialAuthenticationValidation } from "@/actions/socialAuthentication.action";
import { useSignupStore } from "@/store/userSignUpStore";
import { Card, CardContent } from "@/components/ui/card";
import toast from "react-hot-toast";
import { useCookies } from "react-cookie";
import { jwtDecode } from "jwt-decode";
import { UserJwtPayload } from "@/typescript/interfaces";
import { authStore } from "@/store/authStore";

const SocialCallbackClient = () => {
  const searchParams = useSearchParams();
  const router = useRouter();
  const { setUserData } = useSignupStore();
  const [, setCookies] = useCookies(["token"]);
  const { setIsAuthenticated, setUser } = authStore((state) => state);

  useEffect(() => {
    const validate = async () => {
      const state = searchParams.get("state");
      const code = searchParams.get("code");
      let provider = "";
      let type = "auth";

      if (!state || !code) {
        console.warn("Missing state or code in the URL parameters");
        router.push("/");
        return;
      }

      try {
        const parsed = JSON.parse(decodeURIComponent(state));
        provider = parsed.provider;
        type = parsed.type || "auth";
      } catch (error) {
        console.warn("State is not valid JSON, falling back", error);
        provider = state;
        type = "auth";
      }

      const data = {
        type,
        provider,
        code,
      };

      try {
        const response = await socialAuthenticationValidation(data);
        if (response.status >= 200 && response.status < 300) {
          switch (response.type) {
            case "signup":
              const userInfo = response.userInfo;
              setUserData({
                email: userInfo.email,
                firstName: userInfo.firstName,
                lastName: userInfo.lastName,
              });
              router.push("/auth/register");
              break;
            case "connect":
              toast.success(response.message);
              router.push("/dashboard/settings");
              break;
            case "login":
              setCookies("token", response.token, {
                path: "/",
                secure: false,
                maxAge: 30 * 24 * 60 * 60,
              });
              const decodedToken: UserJwtPayload = jwtDecode(response.token);
              setUser({
                email: decodedToken.email as string,
                firstName: decodedToken.firstName as string,
                lastName: decodedToken.lastName as string,
                _id: decodedToken.userId as string,
                avatar: {
                  originalName: decodedToken.avatar.originalName as string,
                  fileName: decodedToken.avatar.fileName as string,
                  fileSize: decodedToken.avatar.fileSize as number,
                  mimeType: decodedToken.avatar.mimeType as string,
                  filePath: decodedToken.avatar.filePath as string,
                },
              });
              setIsAuthenticated(true);
              toast.success(response.message);
              router.push("/dashboard/settings");
              break;
            default:
              router.push("/auth/login");
          }
        } else {
          console.log("Social authentication failed");
          router.push("/");
        }
      } catch (err) {
        console.log("Validation failed", err);
        router.push("/");
      }
    };

    validate();
  }, [searchParams]);

  return (
    <section className="h-[90vh]">
      <div className="container h-full flex items-center justify-center">
        <Card className="w-full max-w-md text-center">
          <CardContent className="space-y-2">
            <p className="text-lg text-gray-600">Validating your social account... 🔄</p>
            <p className="text-sm text-gray-500">
              Please wait... It will be redirect automatically.
            </p>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default SocialCallbackClient;
