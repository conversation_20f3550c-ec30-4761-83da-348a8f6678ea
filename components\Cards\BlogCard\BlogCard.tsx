import React from "react";
import Link from "next/link";
import Image from "next/image";
import { Blog } from "@/typescript/types";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { DynamicIcon } from "lucide-react/dynamic";

interface BlogCardProps {
  blog: Blog;
}

const BlogCard = ({ blog }: BlogCardProps) => {
  const { title, summary, thumbnail } = blog;
  const thumbnailURL = process.env.NEXT_PUBLIC_CDN_URL + "/blog/" + thumbnail;
  return (
    <Card>
      <CardHeader>
        <Image
          src={thumbnailURL}
          alt={title}
          width={800}
          height={800}
          blurDataURL={thumbnailURL}
          className="rounded-lg"
        />
      </CardHeader>
      <CardContent className="space-y-2">
        <CardTitle className="text-xl text-gray-700">{title || "Blog Title"}</CardTitle>
        <p className="text-sm text-gray-500">{summary || "Blog Summary"}</p>
      </CardContent>
      <CardFooter>
        <Button asChild variant={"link"} className="text-gray-700">
          <Link href={`/blog/${blog.slug}`}>
            View More <DynamicIcon name="move-right" />
          </Link>
        </Button>
      </CardFooter>
    </Card>
  );
};

export default BlogCard;
