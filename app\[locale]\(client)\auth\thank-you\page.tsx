import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON>eader } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";

const ThankYouPage = () => {
  return (
    <section className="py-[127px]">
      <div className="container">
        <Card className="max-w-3xl mx-auto p-10 lg:p-20">
          <CardHeader className="justify-center">
            <div className="flex items-center justify-center w-24 h-24 md:w-[110px] md:h-[110px] bg-primary rounded-full">
              <DynamicIcon name="check" size={60} color="white" />
            </div>
          </CardHeader>
          <CardContent className="space-y-[15px] text-center">
            <h2 className="text-5xl md:text-[57px] text-[#555555] font-semibold">Thank You</h2>
            <p className="text-base md:text-xl text-[#555555]">Your registration is complete.</p>
            <p className="text-base md:text-xl text-[#555555]">
              Please check your email for username and password.
            </p>
          </CardContent>
        </Card>
      </div>
    </section>
  );
};

export default ThankYouPage;
