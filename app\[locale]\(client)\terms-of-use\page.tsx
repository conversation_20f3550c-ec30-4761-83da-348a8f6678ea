import React from "react";
import TopBanner from "@/components/common/TopBanner/TopBanner";
import { TermsOfUse } from "@/typescript/types";
import { getTranslations } from "next-intl/server";

const TermsOfUsePage = async () => {
  const t = await getTranslations("TermsOfUsePage");
  const title = t("title");
  const description = t("description");
  const contents = t.raw("contents");

  return (
    <>
      <TopBanner title={title} />
      <section className="py-[60px]">
        <div className="container">
          <div className="space-y-2 text-gray-700">
            <p className="text-base lg:text-[22px] font-semibold">
              In effect for Gayroom8 Members registered on the Platform after April 27, 2023.
            </p>
            <p className="text-base lg:text-[22px]">Last update: May 25, 2023</p>
          </div>
          <hr className="my-5" />
          <div className="text-gray-700 space-y-[30px] text-base lg:text-[22px] ">
            <p>{description}</p>
            {contents.map((content: TermsOfUse, idx: number) => (
              <TermsOfUseContent key={idx} {...content} />
            ))}
          </div>
        </div>
      </section>
    </>
  );
};

const TermsOfUseContent = ({ title, contents }: TermsOfUse) => {
  return (
    <div className="space-y-3">
      <h3 className="text-base lg:text-[22px] font-semibold">{title}</h3>
      <ul className="space-y-[30px]">
        {contents.map((content: string, idx: number) => (
          <li key={idx}>{content}</li>
        ))}
      </ul>
    </div>
  );
};

export default TermsOfUsePage;
