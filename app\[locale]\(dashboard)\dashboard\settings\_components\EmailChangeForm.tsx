"use client";

import React, { useState } from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>Trigger,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { InputOTP, InputOTPGroup, InputOTPSlot } from "@/components/ui/input-otp";
import { UserProfile } from "@/typescript/interfaces";
import { sendOtpToOldEmail, verifyNewOtp, verifyOldOtp } from "@/actions/user.action";
import { useCookies } from "react-cookie";
import toast from "react-hot-toast";

// Step 1: Enter new email
const newEmailSchema = z.object({
  newEmail: z.string().email("Invalid email"),
});

// Step 2: Verify old email OTP
const oldOtpSchema = z.object({
  oldOtp: z.string().min(4, "OTP must be 4 digits"),
});

// Step 3: Verify new email OTP
const newOtpSchema = z.object({
  newOtp: z.string().min(4, "OTP must be 4 digits"),
});

const EmailChangeForm = ({ user }: { user: UserProfile }) => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className="text-2xl text-secondary font-medium flex items-center gap-2.5">
          <DynamicIcon name="send" size={24} /> Update Email
        </CardTitle>
      </CardHeader>
      <CardContent className="flex justify-between items-start md:items-center gap-4 flex-col md:flex-row">
        <p>{user?.email || "N/A"}</p>
        <ChangeEmailForm currentEmail={user?.email} />
      </CardContent>
    </Card>
  );
};

const ChangeEmailForm = ({ currentEmail }: { currentEmail?: string }) => {
  const [cookies, setCookies] = useCookies(["token"]);
  const [step, setStep] = useState<"newEmail" | "oldOtp" | "newOtp" | "done">("newEmail");
  const [dialogOpen, setDialogOpen] = useState(false);
  // const [enteredNewEmail, setEnteredNewEmail] = useState("");

  const newEmailForm = useForm({
    resolver: zodResolver(newEmailSchema),
    defaultValues: { newEmail: "" },
  });

  const oldOtpForm = useForm({
    resolver: zodResolver(oldOtpSchema),
    defaultValues: { oldOtp: "" },
  });

  const newOtpForm = useForm({
    resolver: zodResolver(newOtpSchema),
    defaultValues: { newOtp: "" },
  });

  // Step 1: User enters new email
  const handleNewEmailSubmit = async ({ newEmail }: { newEmail: string }) => {
    // setEnteredNewEmail(newEmail);

    const res = await sendOtpToOldEmail(newEmail, cookies.token);
    if (res.status >= 200 && res.status < 300) {
      toast.success(res.message);
      setStep("oldOtp");
      return;
    }
  };

  // Step 2: Verify old email OTP
  const handleOldOtpSubmit = async ({ oldOtp }: { oldOtp: string }) => {
    const res = await verifyOldOtp(oldOtp, cookies.token);
    if (res.status >= 200 && res.status < 300) {
      toast.success(res.message);
      setStep("newOtp");
      return;
    }
  };

  // Step 3: Verify new email OTP
  const handleNewOtpSubmit = async ({ newOtp }: { newOtp: string }) => {
    const res = await verifyNewOtp(newOtp, cookies.token);
    if (res.status >= 200 && res.status < 300) {
      toast.success(res.message);
      setCookies("token", res.token);
      setStep("done");
      setDialogOpen(false);
      return;
    }
  };

  return (
    <Dialog open={dialogOpen} onOpenChange={setDialogOpen}>
      <DialogTrigger asChild>
        <Button className="rounded-full">Update Email</Button>
      </DialogTrigger>

      <DialogContent>
        <DialogHeader>
          <DialogTitle className="text-[#555555]">Change Email</DialogTitle>
        </DialogHeader>

        {/* STEP 1: Enter New Email */}
        {step === "newEmail" && (
          <Form {...newEmailForm}>
            <form onSubmit={newEmailForm.handleSubmit(handleNewEmailSubmit)} className="space-y-4">
              <FormField
                control={newEmailForm.control}
                name="newEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">New Email</FormLabel>
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="<EMAIL>"
                        {...field}
                        className="input-field"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full px-20 py-5 rounded-full">
                Send OTP to Old Email
              </Button>
            </form>
          </Form>
        )}

        {/* STEP 2: Verify Old Email OTP */}
        {step === "oldOtp" && (
          <Form {...oldOtpForm}>
            <form onSubmit={oldOtpForm.handleSubmit(handleOldOtpSubmit)} className="space-y-4">
              <FormField
                control={oldOtpForm.control}
                name="oldOtp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">OTP sent to {currentEmail}</FormLabel>
                    <FormControl>
                      <div className="flex items-center justify-center">
                        <InputOTP maxLength={6} {...field}>
                          <InputOTPGroup>
                            <InputOTPSlot index={0} />
                            <InputOTPSlot index={1} />
                            <InputOTPSlot index={2} />
                            <InputOTPSlot index={3} />
                            <InputOTPSlot index={4} />
                            <InputOTPSlot index={5} />
                          </InputOTPGroup>
                        </InputOTP>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full px-20 py-5 rounded-full">
                Verify OTP & Send to New Email
              </Button>
            </form>
          </Form>
        )}

        {/* STEP 3: Verify New Email OTP */}
        {step === "newOtp" && (
          <Form {...newOtpForm}>
            <form onSubmit={newOtpForm.handleSubmit(handleNewOtpSubmit)} className="space-y-4">
              <FormField
                control={newOtpForm.control}
                name="newOtp"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="input-label">OTP sent to {currentEmail}</FormLabel>
                    <FormControl>
                      <div className="flex items-center justify-center">
                        <InputOTP maxLength={6} {...field}>
                          <InputOTPGroup>
                            <InputOTPSlot index={0} />
                            <InputOTPSlot index={1} />
                            <InputOTPSlot index={2} />
                            <InputOTPSlot index={3} />
                            <InputOTPSlot index={4} />
                            <InputOTPSlot index={5} />
                          </InputOTPGroup>
                        </InputOTP>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <Button type="submit" className="w-full px-20 py-5 rounded-full">
                Verify & Change Email
              </Button>
            </form>
          </Form>
        )}

        {/* STEP 4: Done */}
        {step === "done" && <p>✅ Email changed successfully!</p>}
      </DialogContent>
    </Dialog>
  );
};

export default EmailChangeForm;
