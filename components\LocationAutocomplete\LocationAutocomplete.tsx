"use client";

import { useRef, useEffect, useState } from "react";
import { Autocomplete } from "@react-google-maps/api";
import { cn } from "@/lib/utils";

interface LocationAutocompleteProps {
  form?: any;
  formValue?: string;
  onSelect?: (data: { address: string; latLng: { lat: number; lng: number } }) => void;
  className?: string;
  placeholder?: string;
}

export default function LocationAutocomplete({
  form,
  formValue = "",
  onSelect,
  className = "",
  placeholder = "Search location...",
}: LocationAutocompleteProps) {
  const inputRef = useRef<HTMLInputElement>(null);
  const autocompleteRef = useRef<google.maps.places.Autocomplete | null>(null);
  const [isReady, setIsReady] = useState(false);

  // Delay rendering until `google.maps` is available
  useEffect(() => {
    if (typeof window !== "undefined" && window.google?.maps?.places) {
      setIsReady(true);
    }
  }, []);

  const handlePlaceChanged = () => {
    const place = autocompleteRef.current?.getPlace();
    if (!place || !place.geometry?.location) return;

    const address = place.formatted_address || "";
    const location = place.geometry.location;
    const latLng = {
      lat: location.lat(),
      lng: location.lng(),
    };

    form?.setValue?.(formValue, address, {
      shouldDirty: true,
      shouldValidate: true,
    });

    onSelect?.({ address, latLng });
  };

  if (!isReady) {
    return <div className="text-gray-500">Loading location search...</div>;
  }

  return (
    <Autocomplete
      onLoad={(autocomplete) => (autocompleteRef.current = autocomplete)}
      onPlaceChanged={handlePlaceChanged}
    >
      <input
        type="text"
        ref={inputRef}
        placeholder={placeholder}
        className={cn(
          "w-full border bg-white px-4 py-2 rounded-sm outline-none text-sm",
          className,
        )}
      />
    </Autocomplete>
  );
}
