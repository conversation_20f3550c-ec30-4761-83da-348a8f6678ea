import { dynamicIconImports } from "lucide-react/dynamic";
import { IconType } from "react-icons";

export interface Navigation {
  label: string;
  href: string;
}

export interface IOffcanvasNavigation extends Navigation {
  icon: keyof typeof dynamicIconImports;
}

export interface ISocialLink extends Navigation {
  icon: IconType;
}

export interface IFooterLinks {
  title: string;
  links: Navigation[];
}

export interface PropertyData {
  _id?: string;
  user?: UserProfile;
  fullAddress: string;
  location?: [number, number] | { coordinates: [number, number] };
  locationLabel: string;
  spaceType: string;
  residenceType: string;
  size: string;
  bedrooms: string;
  bathrooms: string;
  ownerOccupied: string;
  numPeopleInHome: string;
  amenities: string[];
  parking: string[];
  neighborhood: string[];
  currentPets: string[];
  allowedPets: string[];
  smokeCigarettes: string;
  smokeMarijuana: string;
  overnightGuestsAllowed: string;
  homeDescription: string;
  availabilityDate: string;
  availabilityDuration: number;
  minimumDuration: number;
  monthlyRent: number;
  depositAmount: number;
  leaseRequired: string;
  requiredReferences: string;
  utilitiesIncluded: string;
  furnished: string;
  bedroomSize: string;
  brightness: string;
  bathroom: string;
  roomFeatures: string[];
  spacePhotos: string[];
  preferredGenderIdentity: string[];
  preferredSexualOrientation: string[];
  preferredAgeRange: string[];
  idealRoommateDescription: string;
  idealTenantDescription: string;
  rentalStartDate: string;
  rentalDuration: number;
  maxMonthlyBudget: number;
  willingToSignRentalAgreement: string;
  wantFurnished: string;
  pets: string;
  parkingRequired: string;
  preferredSmokingHabits: string[];
  preferredRoommateGenderIdentity: string[];
  preferredRoommateSexualOrientation: string[];
  preferredRoommateAgeRange: string[];
  preferredRoommateSmokingHabits: string[];
  photos:
    | {
        originalName: string;
        fileName: string;
        fileSize: number;
        mimeType: string;
        filePath: string;
      }[]
    | [];
}

export interface Avatar {
  originalName: string;
  fileName: string;
  fileSize: number;
  mimeType: string;
  filePath: string;
}

export interface Membership {
  membershipId: {
    _id: string;
    name: string;
    price: number;
    duration: number;
  };
  startDate: string;
  endDate: string;
  status: string;
  _id: string;
}

export interface UserProfile {
  _id?: string;
  firstName: string;
  lastName: string;
  email: string;
  intent?: string;
  spaceType?: string;
  genderIdentity?: string;
  sexualOrientation?: string;
  age?: number;
  smokeCigarettes?: string;
  smokeMarijuana?: string;
  workFromHome?: string;
  travel?: string;
  cleanliness?: string;
  describeMyselfAs?: string[];
  zodiac?: string;
  selfDescription?: string;
  fluentLanguages?: string[];
  avatar: Avatar;
  isAccountVerified?: boolean;
  isIdVerified?: boolean;
  profileStatus?: boolean;
  listingStatus?: boolean;
  phone?: string;
  notificationPreferences?: NotificationPreferences;
  preferredLocation?: [number, number];
  preferredLocationLabel?: string;
  rentalStartDate?: string;
  rentalDuration?: number;
  maxMonthlyBudget?: number;
  willingToSignRentalAgreement?: string;
  wantFurnished?: string;
  bedroomSize?: string;
  bathroom?: string;
  bedrooms?: string;
  bathrooms?: string;
  pets?: string;
  parkingRequired?: string;
  preferredGenderIdentity?: string[];
  preferredSexualOrientation?: string[];
  preferredAgeRange?: string[];
  preferredSmokingHabits?: string[];
  idealRoommateDescription?: string;
  idealTenantDescription?: string;
  membership?: Membership;
  isGoogleConnected?: boolean;
  isFacebookConnected?: boolean;
}

export interface NotificationPreferences {
  newListing: boolean;
  newMessage: boolean;
  favoriteListing: boolean;
  finishProfile: boolean;
  finishListing: boolean;
  chatRequest: boolean;
}

export interface ApiResponse<T> {
  message: string;
  status?: number;
  data?: T;
}

export interface UserJwtPayload {
  userId: string;
  email: string;
  firstName: string;
  lastName: string;
  avatar: {
    originalName: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    filePath: string;
  };
}

export interface StepItem {
  label: string;
  icon: keyof typeof dynamicIconImports;
}

export interface Conversation {
  _id: string;
  members: {
    _id: string;
    user: UserProfile;
    createdAt: string;
    updatedAt: string;
  }[];
  isDeleted: boolean;
  deletedAt: null;
  createdAt: string;
  updatedAt: string;
}

export interface Message {
  _id: string;
  conversation?: string;
  user: string | UserProfile; // Can be either user ID string or full user object
  message: string;
  messageType: string;
  isRead: boolean;
  createdAt?: string;
  updatedAt?: string;
  senderUser?: string | UserProfile; // Optional field to preserve original user data
}

export interface ChatContent {
  messageType: string;
  chat?: string;
  message: string;
  image?: {
    originalName: string;
    fileName: string;
    fileSize: number;
    mimeType: string;
    filePath: string;
  };
}
