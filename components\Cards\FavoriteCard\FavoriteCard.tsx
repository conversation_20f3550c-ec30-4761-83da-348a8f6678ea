"use client";

import React from "react";
import Link from "next/link";
import AddToWishlist from "@/components/Buttons/AddToWishlist/AddToWishlist";
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader, CardTitle } from "@/components/ui/card";
import { DynamicIcon } from "lucide-react/dynamic";
import { Badge } from "@/components/ui/badge";
import { roomTypeLabels } from "@/constants/constants";
import FavoriteImagesSlide from "./FavoriteCardImageSlide";
import FavoriteCardAction from "./FavoriteCardAction";
import { amenitiesLabel } from "@/constants/listing.constants";

interface ListingCardProps {
  listing: any;
  showListingActions?: boolean;
}

const FavoriteCard = ({ listing, showListingActions = false }: ListingCardProps) => {
  return (
    <Card className="text-gray-700 py-3 relative">
      <CardHeader className="px-3">
        <FavoriteImagesSlide images={listing?.photos} />
        {/* Add to Wishlist */}
        <AddToWishlist
          className="absolute top-5 right-5 z-10 rounded-full cursor-pointer"
          listingId={listing._id as string}
        />
      </CardHeader>
      <CardContent className="px-3 space-y-4">
        <div className="flex justify-between items-center">
          <CardTitle className="flex items-center gap-2">
            <DynamicIcon name="map-pin" size={20} color="#2980B9" />
            {listing?.fullAddress}
          </CardTitle>
          {showListingActions && <FavoriteCardAction />}
        </div>
        <ul className="flex flex-wrap items-center gap-3">
          {listing?.amenities?.map((amenity: string, idx: number) => {
            const label = amenitiesLabel[amenity];
            return (
              <li key={idx} className="text-sm">
                • {label}
              </li>
            );
          })}
        </ul>
        <div className="flex items-center gap-2">
          <Badge className="px-4 py-2 bg-primary/10 text-secondary">
            {roomTypeLabels[listing?.spaceType]}
          </Badge>
          <Badge className="px-4 py-2 bg-secondary/10 text-secondary capitalize">
            {listing?.residenceType}
          </Badge>
          {/* <Badge className="px-4 py-2 bg-secondary/10 text-secondary">{availabilityDate}</Badge> */}
        </div>
      </CardContent>
      <CardFooter className="px-3 justify-between">
        <Link
          href={`/explore-spaces/${listing._id}`}
          className="flex items-center gap-2 hover:underline"
        >
          View More <DynamicIcon name="move-right" size={16} />
        </Link>
        {/* <p>
          <span className="font-bold text-lg text-primary">${monthlyRent}</span>/month
        </p> */}
      </CardFooter>
    </Card>
  );
};

export default FavoriteCard;
