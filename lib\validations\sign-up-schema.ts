import { z } from "zod";

export const registerSchema = z.object({
  firstName: z.string().min(2, "First name is required, minimum 2 characters"),
  lastName: z.string().min(2, "Last name is required, minimum 2 characters"),
  email: z.string().email(),
  intent: z.string().min(1, "Intent is required"),
  spaceType: z.string().min(1, "Space Type is required"),
});

export const aboutYouSchema = z.object({
  genderIdentity: z.string().min(1),
  sexualOrientation: z.string().min(1),
  age: z.number().min(10).optional(),
  smokeCigarettes: z.string().optional(),
  smokeMarijuana: z.string().optional(),
  workFromHome: z.string().optional(),
  travel: z.string().optional(),
  cleanliness: z.string().min(1).optional(),
  describeMyselfAs: z.array(z.string()).min(1, "I Describe Myself As is required"),
  fluentLanguages: z.array(z.string()).min(1, "Fluent Languages is required"),
  zodiac: z.string().optional(),
  selfDescription: z.string().min(50, "The self description must be at least 50 characters."),
});

export const aboutYouFindSchema = z.object({
  genderIdentity: z.string().min(1, "Gender Identity is required"),
  sexualOrientation: z.string().min(1, "Sexual Orientation is required"),
  age: z.number().min(10, "Age must be at least 10 years old"),
  smokeCigarettes: z.string().min(1, "Smoke Cigarettes is required"),
  smokeMarijuana: z.string().min(1, "Smoke Marijuana is required"),
  workFromHome: z.string().min(1, "Work From Home is required"),
  travel: z.string().min(1, "Travel is required"),
  cleanliness: z.string().min(1, "Cleanliness is required"),
  fluentLanguages: z.array(z.string()).min(1, "Fluent Languages is required"),
  describeMyselfAs: z.array(z.string()).min(1, "I Describe Myself As is required"),
  zodiac: z.string().optional(),
  selfDescription: z.string().min(1, "Self Description is required"),
});

// About You Entire Place Schema
export const aboutYouEntireSchema = z.object({
  genderIdentity: z.string().min(1),
  sexualOrientation: z.string().min(1),
  selfDescription: z.string().min(50, "The self description must be at least 50 characters."),
});

export const homeSchema = z.object({
  fullAddress: z.string().min(1),
  residenceType: z.string().min(1),
  size: z.string(),
  bedrooms: z.string().min(1),
  bathrooms: z.string().min(1),
  ownerOccupied: z.string().min(1),
  numPeopleInHome: z.string().min(1),
  amenities: z.array(z.string()).min(1),
  parking: z.array(z.string()).min(1),
  neighborhood: z.array(z.string()).min(1),
  currentPets: z.array(z.string()).min(1),
  allowedPets: z.array(z.string()).min(1),
  smokeCigarettes: z.string().min(1),
  smokeMarijuana: z.string().min(1),
  overnightGuestsAllowed: z.string().min(1),
  homeDescription: z.string().min(50, "The home description must be at least 50 characters."),
  location: z.array(z.number(), z.number()),
  locationLabel: z.string(),
});

// The Space Schema
export const spaceSchema = z.object({
  fullAddress: z.string().min(1),
  residenceType: z.string().min(1, "Residence Type is required"),
  size: z.string().min(1, "Size is required"),
  bedrooms: z.string().min(1, "Bedrooms is required"),
  bathrooms: z.string().min(1, "Bathrooms is required"),
  brightness: z.string().min(1, "Brightness is required"),
  amenities: z.array(z.string()).nonempty("Amenities is required"),
  parking: z.array(z.string()).nonempty("Parking is required"),
  neighborhood: z.array(z.string()).nonempty("Neighborhood is required"),
  allowedPets: z.array(z.string()).nonempty("Allowed Pets is required"),
  smokeCigarettes: z.string().min(1, "Smoke Cigarettes is required"),
  smokeMarijuana: z.string().min(1, "Smoke Marijuana is required"),
  homeDescription: z.string().min(1, "Home Description is required"),
  location: z.array(z.number(), z.number()).nonempty("Location is required"),
  locationLabel: z.string().min(1, "Location Label is required"),
});

// The Room Schema
export const roomSchema = z.object({
  availabilityDate: z.string().min(1, "Availability Date is required"),
  availabilityDuration: z.number().min(1, "Availability Duration is required"),
  minimumDuration: z.number().min(1, "Minimum Duration is required"),
  monthlyRent: z.coerce.number().min(1, "Monthly Rent is required"),
  depositAmount: z.coerce.number().min(1, "Deposit Amount is required"),
  leaseRequired: z.string().min(1, "Lease Required is required"),
  requiredReferences: z.string().min(1, "Required References is required"),
  utilitiesIncluded: z.string().min(1, "Utilities Included is required"),
  furnished: z.string().min(1, "Furnished is required"),
  bedroomSize: z.string().min(1, "Bedroom Size is required"),
  brightness: z.string().min(1, "Brightness is required"),
  bathroom: z.string().min(1, "Bathroom is required"),
  roomFeatures: z.array(z.string()).min(1, "Room Features is required"),
});

// Rental Basics Schema
export const rentalBasicSchema = z.object({
  availabilityDate: z.string().min(1, "Availability Date is required"),
  availabilityDuration: z.number().min(1, "Availability Duration is required"),
  minimumDuration: z.number().min(1, "Minimum Duration is required"),
  monthlyRent: z.coerce.number().min(1, "Monthly Rent is required"),
  depositAmount: z.coerce.number().min(1, "Deposit Amount is required"),
  leaseRequired: z.string().min(1, "Lease Required is required"),
  requiredReferences: z.string().min(1, "Required References is required"),
  utilitiesIncluded: z.string().min(1, "Utilities Included is required"),
  furnished: z.string().min(1, "Furnished is required"),
});

export const roommatePrefsSchema = z.object({
  preferredGenderIdentity: z.array(z.string()).min(1, "Preferred Gender Identity is required"),
  preferredSexualOrientation: z
    .array(z.string())
    .min(1, "Preferred Sexual Orientation is required"),
  preferredAgeRange: z.array(z.string()).min(1, "Preferred Age Range is required"),
  preferredSmokingHabits: z.array(z.string()).min(1, "Preferred Smoking Habits is required"),
  idealRoommateDescription: z
    .string()
    .min(150, "The ideal roommate description must be at least 150 characters."),
});

// Roommate Preferences Find Schema
export const roommateFindPrefsSchema = z.object({
  preferredGenderIdentity: z.array(z.string()).min(1, "Preferred Gender Identity is required"),
  preferredSexualOrientation: z
    .array(z.string())
    .min(1, "Preferred Sexual Orientation is required"),
  preferredAgeRange: z.array(z.string()).min(1, "Preferred Age Range is required"),
  preferredSmokingHabits: z.array(z.string()).min(1, "Preferred Smoking Habits is required"),
});

export const rentalPrefsSchema = z.object({
  preferredLocation: z.array(z.number(), z.number()).min(1, "Preferred Location is required"),
  preferredLocationLabel: z.string().min(1, "Preferred Location Label is required"),
  rentalStartDate: z.string().nonempty("Rental Start Date is required"),
  rentalDuration: z.coerce.number().min(0, "Rental Duration is required"),
  maxMonthlyBudget: z.coerce.number().min(1, "Max Monthly Budget is required"),
  willingToSignRentalAgreement: z.string().min(1, "Willing to Sign Rental Agreement is required"),
  wantFurnished: z.string().min(1, "Want Furnished is required"),
  bedrooms: z.string().min(1, "Bedrooms is required"),
  bathrooms: z.string().min(1, "Bathrooms is required"),
  pets: z.string().min(1, "Pets is required"),
  parkingRequired: z.string().min(1, "Parking Required is required"),
});

// Upload Space Photos Schema
export const spacePhotosSchema = z.object({
  spacePhotos: z.array(z.string()),
});

// Tenant Preferences Schema
export const tenantPrefsSchema = z.object({
  idealTenantDescription: z
    .string()
    .min(50, "The ideal tenant description must be at least 50 characters."),
});

// Find Private Room Schema
export const findPrivateRoomSchema = z.object({
  preferredLocation: z.array(z.number(), z.number()).min(1, "Preferred Location is required"),
  preferredLocationLabel: z.string(),
  rentalStartDate: z.string().min(1, "Rental Start Date is required"),
  rentalDuration: z.coerce.number().min(1, "Rental Duration is required"),
  maxMonthlyBudget: z.coerce.number().min(1, "Max Monthly Budget is required"),
  willingToSignRentalAgreement: z.string().min(1, "Willing to Sign Rental Agreement is required"),
  wantFurnished: z.string().min(1, "Want Furnished is required"),
  bathroom: z.string().min(1, "Bathroom is required"),
  bedroomSize: z.string().min(1, "Bedroom Size is required"),
  pets: z.string().min(1, "Pets is required"),
  parkingRequired: z.string().min(1, "Parking Required is required"),
});

export type SignupData = z.infer<typeof registerSchema>;
export type AboutYouData = z.infer<typeof aboutYouSchema>;
export type HomeData = z.infer<typeof homeSchema>;
export type RoomData = z.infer<typeof roomSchema>;
export type RoommatePrefsData = z.infer<typeof roommatePrefsSchema>;
export type RentalPrefsData = z.infer<typeof rentalPrefsSchema>;
export type SpacePhotosData = z.infer<typeof spacePhotosSchema>;
export type AboutYouEntireData = z.infer<typeof aboutYouEntireSchema>;
export type SpaceData = z.infer<typeof spaceSchema>;
export type RentalBasicData = z.infer<typeof rentalBasicSchema>;
export type TenantPrefsData = z.infer<typeof tenantPrefsSchema>;
export type AboutYouFindData = z.infer<typeof aboutYouFindSchema>;
export type FindPrivateRoomData = z.infer<typeof findPrivateRoomSchema>;
export type RoommateFindPrefsData = z.infer<typeof roommateFindPrefsSchema>;
